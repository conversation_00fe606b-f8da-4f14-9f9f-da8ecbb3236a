import 'package:json_annotation/json_annotation.dart';
import 'package:words/domain/study_record.dart';
import 'dart:convert';

import 'package:words/domain/support/is_json_serialable.dart';
import 'package:words/domain/word.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'package:words/commonSupport/utils/math_util.dart';
import 'package:words/commonSupport/utils/type_serial_util.dart';
import 'ebbinghaus_point.dart';

part 'cur_study_record.g.dart';

@JsonSerializable()
class CurStudyRecord implements IsJsonSerialable{
	@JsonKey(name: "ID")
	String? id;

	@Json<PERSON>ey(name: "CONTENT_SEARCH_ID")
	String? contentSearchId;

	@JsonKey(name: "CONTENT_ID")
	String? contentId;

	@Json<PERSON>ey(name: "USER_ID")
  String? userId;

	@J<PERSON><PERSON><PERSON>(name: "STUDY_LEVEL")
	int? studyLevel;

	@Json<PERSON><PERSON>(name: "CUR_STUDY_LEVEL")
	int? curStudyLevel;

	@JsonKey(name: "CUR_ADJ_STUDY_LEVEL")
	int? curAdjStudyLevel;

	@JsonKey(name: "FIRST_JUDGE", fromJson: TypeSerialUtil.dynamicToBool, toJson: TypeSerialUtil.boolToInt)
	bool? firstJudge;

	@JsonKey(name: "FIRST_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? firstStudyDate;

	@JsonKey(name: "ADD_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? addDate;

	@JsonKey(name: "LAST_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? lastStudyDate;

	@JsonKey(name: "MIN_NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? minNextStudyDate;

	@JsonKey(name: "NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? nextStudyDate;

	@JsonKey(name: "CUR_EBBINGHAUS_STR")
	String? curEbbinghausStr;

	@JsonKey(name: "BACK_NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? backNextStudyDate; //备份下次开始时间，统计时计算用

	@JsonKey(name: "NEVER_SEEN", fromJson: TypeSerialUtil.dynamicToBool, toJson: TypeSerialUtil.boolToInt)
	bool? neverSeen;

	@JsonKey(name: "ORI_STUDY_LEVEL")
	int? oriStudyLevel;

	@JsonKey(name: "ORI_NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? oriNextStudyDate;

	@JsonKey(name: "ORI_MIN_NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? oriMinNextStudyDate;

	@JsonKey(ignore: true)
	int? noUse = 1; //0:复习旧词 1：新学词 2:即将在当天到期的词 3:即将在当天之后到期的词 -1：无用充时间的词
	               //默认值置为1，新学词，盢为没有学习记录时，就用默认值显示，如果有学习记录，则会根据记录置值

	@JsonKey(ignore: true)
	Word? curWord;

	CurStudyRecord();

	CurStudyRecord.fromRecord(StudyRecord record) {
		this.id = record.id;
		this.contentSearchId = record.contentSearchId;
		this.contentId = record.contentId;
		this.userId = record.userId;
		this.studyLevel = record.studyLevel;
		this.firstStudyDate = record.firstStudyDate;
		this.addDate =  record.addDate;
		this.lastStudyDate = record.lastStudyDate;
		this.minNextStudyDate = record.minNextStudyDate;
		this.nextStudyDate = record.nextStudyDate;
		this.curEbbinghausStr = record.curEbbinghausStr;
		this.backNextStudyDate = this.nextStudyDate;
		this.neverSeen = record.neverSeen;
		this.oriStudyLevel = record.studyLevel;
		this.oriNextStudyDate = record.nextStudyDate;
		this.oriMinNextStudyDate = record.minNextStudyDate;
		this.curAdjStudyLevel = 0;

		if (studyLevel == 0) {//StudyLevel 为 0，是新学单词，curStudyLevel从0开始
			curStudyLevel = 0;
		} else {
			curStudyLevel = SystemConfig.studyEbbinghausPoint!.ebbinghausPoint!.length;
		}
		initNoUse();
		firstJudge = true; //从studyrecord取出存入curStudyRecord时，置首次判断标志为true

	}

	factory CurStudyRecord.fromJson(Map<String, dynamic> json) => _$CurStudyRecordFromJson(json);

	@override
	Map<String, dynamic> toJson() => _$CurStudyRecordToJson(this);

	@override
	fromJson(param) {
		return CurStudyRecord.fromJson(param);
	}

	@override
	String toString() {
		return jsonEncode(this);
	}


	bool primeDataEquals(CurStudyRecord target) {
		if (contentSearchId != target.contentSearchId) return false;
		if (contentId != target.contentId) return false;
		if (userId != target.userId) return false;
		if (studyLevel != target.studyLevel) return false;
		if (curStudyLevel != target.curStudyLevel) return false;
		if (curAdjStudyLevel != target.curAdjStudyLevel) return false;
		if (firstJudge != target.firstJudge) return false;

		// Uncomment the following lines if the corresponding fields are non-null
		// if (firstStudyDate != null && firstStudyDate != target.firstStudyDate) return false;
		// if (addDate != null && addDate != target.addDate) return false;
		// if (lastStudyDate != null && lastStudyDate != target.lastStudyDate) return false;
		// if (minNextStudyDate != null && minNextStudyDate != target.minNextStudyDate) return false;
		// if (nextStudyDate != null && nextStudyDate != target.nextStudyDate) return false;

		if (curEbbinghausStr != target.curEbbinghausStr) return false;

		// Uncomment the following line if backNextStudyDate is non-null
		// if (backNextStudyDate != null && backNextStudyDate != target.backNextStudyDate) return false;

		// if (neverSeen != target.neverSeen) return false;
		if (oriStudyLevel != target.oriStudyLevel) return false;

		// Uncomment the following lines if the corresponding fields are non-null
		// if (oriNextStudyDate != null && oriNextStudyDate != target.oriNextStudyDate) return false;
		// if (oriMinNextStudyDate != null && oriMinNextStudyDate != target.oriMinNextStudyDate) return false;

		if (noUse != target.noUse) return false;

		return true;
	}

	CurStudyRecord silentClone() {
		CurStudyRecord reslut = fromJson(this.toJson());
		reslut.noUse = this.noUse;
		reslut.curWord = this.curWord != null ? this.curWord?.clone() : null;

		return reslut;
	}


	void initNoUse() {
		if ((studyLevel == 0) && (curStudyLevel == null) || (curStudyLevel == 0))
			noUse = 1;
		else
			noUse = 0;
	}

	static CurStudyRecord? getMinRcdFromList(List<CurStudyRecord>? vRcdList) {
		if (CollectionUtil.isEmpty(vRcdList)) {
			return null;
		}

		CurStudyRecord res = vRcdList![0];
		for (CurStudyRecord rcd in vRcdList) {
			if (compare(res, rcd) == 1) {
				res = rcd;
			}
		}
		return res;
	}

	/**************************
	 * return:
	 * 1: o1 > o2
	 * 0: o1 == o2
	 * -1: o1 < o2
	 ***************************/
	static int compare(CurStudyRecord? o1, CurStudyRecord? o2) {
		if (o1 == null) {
			if (o2 == null) {
				return 0;
			} else {
				return -1;
			}
		} else {
			if (o2 == null) {
				return 1;
			}
		}

		// Compare FirstJudge
		if ((o1.firstJudge != null) && (o2.firstJudge != null)) {
			if (o1.firstJudge == false) {
				if (o2.firstJudge == true) {
					return -1;
				}
			} else {
				if (o2.firstJudge == false) {
					return 1;
				}
			}
		}

		// 如果今天4点前会正式到期的，比今天4点后会到期的先学
		int todayBoundary = DateUtil.getTodayBoundary().millisecondsSinceEpoch;
		if (o1.firstJudge == true &&
				o2.firstJudge == true &&
				o1.oriNextStudyDate != null &&
				o2.oriNextStudyDate != null) {
			if (o1.oriNextStudyDate!.millisecondsSinceEpoch < todayBoundary) {
				if (o2.oriNextStudyDate!.millisecondsSinceEpoch >= todayBoundary) {
					return -1;
				}
			} else if (o2.oriNextStudyDate!.millisecondsSinceEpoch < todayBoundary) {
				return 1;
			}
		}

		// 用超时和间隔时间的比值作为判断大小的依据，只在firstJudge都为true时才用超时率来判断先后，-1先，1后
		if (o1.firstJudge == true &&
				o2.firstJudge == true &&
				o1.oriNextStudyDate != null &&
				o2.oriNextStudyDate != null &&
				o1.curEbbinghausStr != null &&
				o2.curEbbinghausStr != null) {
			final now = DateTime.now();
			final o1Rate = o1.calPostponeRate(now);
			final o2Rate = o2.calPostponeRate(now);
			if (o1Rate == null) {
				if (o2Rate == null) {
					return 0;
				} else {
					return 1;
				}
			} else {
				if (o2Rate == null) {
					return -1;
				}
			}

			if (o1Rate > o2Rate) {
				return -1;
			} else if (o1Rate == o2Rate) {
				return 0;
			} else {
				return 1;
			}
		}

		// Compare StudyLevel
		if (o1.studyLevel == null) {
			if (o2.studyLevel == null) {
				return 0;
			} else {
				return 1;
			}
		} else {
			if (o2.studyLevel == null) {
				return -1;
			}
		}

		if (o1.studyLevel! > o2.studyLevel!) {
			return 1;
		}
		if (o1.studyLevel! < o2.studyLevel!) {
			return -1;
		}

		// Compare CurStudyLevel
		if (o1.curStudyLevel == null) {
			if (o2.curStudyLevel == null) {
				return 0;
			} else {
				return 1;
			}
		} else {
			if (o2.curStudyLevel == null) {
				return -1;
			}
		}

		if (o1.curStudyLevel! > o2.curStudyLevel!) {
			return 1;
		}
		if (o1.curStudyLevel! < o2.curStudyLevel!) {
			return -1;
		}

		// Compare NextStudyDate
		if (o1.nextStudyDate == null) {
			if (o2.nextStudyDate == null) {
				return 0;
			} else {
				return 1;
			}
		} else {
			if (o2.nextStudyDate == null) {
				return -1;
			}
		}

		if (o1.nextStudyDate!.millisecondsSinceEpoch > o2.nextStudyDate!.millisecondsSinceEpoch) {
			return 1;
		}
		if (o1.nextStudyDate!.millisecondsSinceEpoch < o2.nextStudyDate!.millisecondsSinceEpoch) {
			return -1;
		}

		if (o1.minNextStudyDate == null) {
			if (o2.minNextStudyDate == null) {
				return 0;
			} else {
				return 1;
			}
		} else {
			if (o2.minNextStudyDate == null) {
				return -1;
			}
		}

		if (o1.minNextStudyDate!.millisecondsSinceEpoch > o2.minNextStudyDate!.millisecondsSinceEpoch) {
			return 1;
		}
		if (o1.minNextStudyDate!.millisecondsSinceEpoch < o2.minNextStudyDate!.millisecondsSinceEpoch) {
			return -1;
		}
		return 0;
	}

	double? calPostponeRate(DateTime now) {
		if (this.oriNextStudyDate == null || this.curEbbinghausStr == null) {
			return null;
		}
		try {
			final ebbPeriod = EbbinghausPoint.calPointFromStr(this.curEbbinghausStr!);
			final postPeriod =
					now.millisecondsSinceEpoch - oriNextStudyDate!.millisecondsSinceEpoch;
			return postPeriod / ebbPeriod.toDouble();
		} catch (e, stackTrace) {
			print(stackTrace);
			return null;
		}
	}


	void calStudyTime(DateTime date, EbbinghausPoint ebbinghausPoint) {
		calStudyTime_full(date, this.curStudyLevel, ebbinghausPoint);
	}
	void calStudyTime_full(DateTime date, int? calLevel, EbbinghausPoint ebbinghausPoint) {
		this.lastStudyDate = date;

		int offset;

		offset = ebbinghausPoint.ebbinghausPoint![calLevel! < 0? 0: calLevel!]; //如果level小于0，则取0

		this.nextStudyDate = new DateTime.fromMillisecondsSinceEpoch(date.millisecondsSinceEpoch + offset);
		this.minNextStudyDate = this.nextStudyDate;
	}



	bool isTooLateToStatic(DateTime evaluTime) {
		if (this.backNextStudyDate == null)
			return true;
		int planDuration =  this.backNextStudyDate!.millisecondsSinceEpoch - this.lastStudyDate!.millisecondsSinceEpoch;
		int evalDuration =  evaluTime!.millisecondsSinceEpoch - this.lastStudyDate!.millisecondsSinceEpoch;
		if (evalDuration > planDuration * (1 + MathUtil.calAtanRatio(planDuration))) //超过计划时间一定比例，大约0.3-0.01，不记入统计
			return true;
		return false;
	}

  static Map<String, CurStudyRecord>? buildIdIndexedMap(List<CurStudyRecord>? curRecordListInDb) {
		if (CollectionUtil.isEmpty(curRecordListInDb))
			return null;
		Map<String, CurStudyRecord> result = {};
		for (CurStudyRecord cRcd in curRecordListInDb!) {
			result[cRcd!.id!] = cRcd;
		}
		return result;
	}
}



