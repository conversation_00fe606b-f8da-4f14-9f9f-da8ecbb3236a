import 'package:sqflite/sqflite.dart';
import 'package:words/domain/word.dart';
import 'support/db_base_provider.dart';

class TbWord extends DBBaseProvider {

  @override
  String get tableName => "WORD";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID text not null,
        SEARCH_ID text,
        OWNER_ID text,
        WORD text,
        MEANING text,
        FREQUENCY int,
        CATEGORY text,
        USER_EDITED int
        )
        ''';
  }

  queryList(int page, {searchText, mean}) async {
    final Database? db = await dataBase;
    List<Map<String, Object?>>? list = [];
    if ((searchText == null || searchText.length == 0) && (mean == null || mean.length == 0)) {
      list = await baseQueryList(page);
    }else{
      var sql = "";
      var args = [];
      if (searchText != null && searchText.length > 0) {
        sql = "SEARCH_ID like ?";
        args = ["$searchText%"];
      }
      if (mean != null && mean.length > 0) {
        if (sql.length > 0) {
          sql += " and ";
        }
        sql += "MEANING like ?";
        args.add("%$mean%");
      }
      list = await db?.query(tableName, where: sql, whereArgs: args, limit: 20, offset: page*20);
    }
    List<Word> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        Word entity = Word.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  rawQueryListBy(String bookId, bool isHeaderLoad, {page,limit,sequence}) async {
    String sql =
    '''
    select 
    WORD.ID, WORD.SEARCH_ID, WORD.OWNER_ID, WORD.WORD, WORD.MEANING, WORD.FREQUENCY, WORD.CATEGORY, WORD.USER_EDITED, BOOK_CONTENT.SEQUENCE 
    from WORD inner join BOOK_CONTENT 
    on WORD.SEARCH_ID=BOOK_CONTENT.CONTENT_SEARCH_ID and BOOK_ID='$bookId'
    ''';
    if (isHeaderLoad) {
      sql += "and SEQUENCE>=${sequence-limit} and SEQUENCE<$sequence order by SEQUENCE";
    }else{
      sql += "and SEQUENCE>$sequence and SEQUENCE<=${(page + 1) * limit + sequence + 1} order by SEQUENCE";
    }
    List<Map<String, Object?>>? list = await baseRawQueryList(sql);
    List<Word> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        Word entity = Word.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  goTo(int sequence, String bookId, {int limit = 75}) async {
    String sql =
    '''
    select 
    WORD.ID, WORD.SEARCH_ID, WORD.OWNER_ID, WORD.WORD, WORD.MEANING, WORD.FREQUENCY, WORD.CATEGORY, WORD.USER_EDITED, BOOK_CONTENT.SEQUENCE 
    from WORD inner join BOOK_CONTENT 
    on WORD.SEARCH_ID=BOOK_CONTENT.CONTENT_SEARCH_ID and BOOK_ID='$bookId'
    and SEQUENCE>${sequence-limit} and SEQUENCE<${sequence+limit} order by SEQUENCE
    ''';
    List<Map<String, Object?>>? list = await baseRawQueryList(sql);
    List<Word> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        Word entity = Word.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  wordListQueryByRecord() async {
    String sql =
    '''
    select 
    WORD.ID, WORD.SEARCH_ID, WORD.OWNER_ID, WORD.WORD, WORD.MEANING, WORD.FREQUENCY, WORD.CATEGORY, WORD.USER_EDITED 
    from WORD inner join STUDY_RECORD 
    on WORD.SEARCH_ID=STUDY_RECORD.CONTENT_SEARCH_ID
    ''';
    List<Map<String, Object?>>? list = await baseRawQueryList(sql);
    List<Word> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        Word entity = Word.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  // 为谁绑定，谁的 id 就是 RELATION_ID
  wordListQueryByRelation(String wordId) async {
    String sql =
    '''
    select 
    WORD.ID, WORD.SEARCH_ID, WORD.OWNER_ID, WORD.WORD, WORD.MEANING, WORD.FREQUENCY, WORD.CATEGORY, WORD.USER_EDITED 
    from WORD inner join WORD_RELATION 
    on WORD.ID=WORD_RELATION.WORD_ID and RELATION_ID='$wordId'
    ''';
    List<Map<String, Object?>>? list = await baseRawQueryList(sql);
    List<Word> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        Word entity = Word.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  randomGetWord(int sec) async {
    String sql =
    '''
    select 
    WORD.ID, WORD.SEARCH_ID, WORD.OWNER_ID, WORD.WORD, WORD.MEANING, WORD.FREQUENCY, WORD.CATEGORY, WORD.USER_EDITED, BOOK_CONTENT.SEQUENCE 
    from WORD inner join BOOK_CONTENT 
    on WORD.SEARCH_ID=BOOK_CONTENT.CONTENT_SEARCH_ID and SEQUENCE='$sec'
    ''';
    List<Map<String, Object?>>? list = await baseRawQueryList(sql);
    Word? _result;
    if (list != null && list.length > 0) {
      _result = Word.fromJson(Map<String, dynamic>.of(list[0]));
    }
    return _result;
  }

  Map<String, dynamic> toMap(Word entity) {
    Map<String, dynamic> map = {
      "ID": entity.id,
      "SEARCH_ID": entity.searchId,
      "OWNER_ID": entity.ownerId,
      "WORD": entity.word,
      "MEANING": entity.meaning,
      "FREQUENCY": entity.frequency,
      "CATEGORY": entity.category,
      "USER_EDITED": entity.userEdited
    };
    return map;
  }


}