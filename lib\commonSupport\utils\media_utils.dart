import 'dart:async';
import 'dart:io';
import 'package:just_audio/just_audio.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/utils/tts/tts_utils.dart';

class MediaUtils {
  static AudioPlayer? _audioPlayer;
  
  static AudioPlayer get audioPlayer {
    _audioPlayer ??= AudioPlayer();
    return _audioPlayer!;
  }

  static Future<void> playMediaFile(String? fileName) async {
    if (StringUtil.isEmpty(fileName))
      return;
    
    try {
      String fullFileName = SystemConfig.SOUND_BASE_PATH + Platform.pathSeparator + MediaUtils.buildMediaFilePath(fileName!);
      final File audioFile = File(fullFileName);
      
      if (await audioFile.exists()) {
        try {
          await audioPlayer.setFilePath(fullFileName);
          await audioPlayer.play();
        } catch (e) {
          print('Error playing audio file: $e');
          // Fallback to TTS if audio file fails
          TtsUtils.speak(fileName);
        }
      } else {
        // Use TTS if file doesn't exist
        TtsUtils.speak(fileName);
      }
    } catch (e) {
      print('Error playing audio: $e');
    }
  }

  static String buildMediaFilePath(String word) {
    StringBuffer buffer = StringBuffer();
    buffer.write(buildMediaRelativeDirPath(word));
    buffer.write(word);
    return buffer.toString();
  }
  static String buildMediaRelativeDirPath(String word) {
    return "${word.toLowerCase()[0]}${Platform.pathSeparator}";
  }

  static bool soundFileExists(String word) {
    File file = File(SystemConfig.SOUND_BASE_PATH + Platform.pathSeparator + buildMediaFilePath(word));
    return file.existsSync();
  }

  static Future<bool> isMp3File(String filePath) async {
    File file = File(filePath);
    if(await file.exists()) {
      List<int> bytes = await file.readAsBytesSync().take(3).toList();
      if(bytes.length == 3) {
        // Check for "ID3" header
        if(bytes[0] == 0x49 && bytes[1] == 0x44 && bytes[2] == 0x33) {
          return true;
        }
        // Check for frame sync bytes
        if(bytes[0] == 0xFF) {
          return true;
        }
      }
    }
    return false;
  }

  static Future<void> stopAudio() async {
    try {
      await audioPlayer.stop();
    } catch (e) {
      print('Error stopping audio: $e');
    }
  }

  static Future<void> dispose() async {
    try {
      await _audioPlayer?.dispose();
      _audioPlayer = null;
    } catch (e) {
      print('Error disposing audio player: $e');
    }
  }
}


