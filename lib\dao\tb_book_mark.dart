import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/book.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/domain/book_mark.dart';

class TbBookMark extends DBBaseProvider {

  @override
  String get tableName => "BOOK_MARK";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        BOOK_ID TEXT,
        USER_ID TEXT,
        CUR_SEQUENCE INTEGER
        )
        ''';
  }

  queryList(int page) async {
    List<Map<String, Object?>>? list = await baseQueryList(page);
    List<BookMark> result = [];
    if (CollectionUtil.nonEmpty(list)) {
      list!.forEach((element) {
        BookMark entity = BookMark.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

}