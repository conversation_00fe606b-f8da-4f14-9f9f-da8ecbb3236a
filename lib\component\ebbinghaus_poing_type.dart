enum EbbinghausPointType {
  Study,
  Repeat
}

extension EbbinghausPointTypeExtensions on EbbinghausPointType {

  String get value {
    switch(this) {
      case EbbinghausPointType.Study:
        return 'Study';
      case EbbinghausPointType.Repeat:
        return 'Repeat';
    }
  }

  static EbbinghausPointType fromValue(String value) {
    switch (value) {
      case 'Study':
        return EbbinghausPointType.Study;
      case 'Repeat':
        return EbbinghausPointType.Repeat;
      default:
        throw ArgumentError('Invalid enum value $value');
    }
  }
}