import 'dart:async';

import 'package:flutter/material.dart';
import 'package:words/biz/system_biz.dart';
import 'package:words/common/system_config.dart';
import 'package:words/common/system_const.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/commonSupport/utils/type_serial_util.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/domain/ebbinghaus_point.dart';
import 'package:words/exception/invalid_ebb_point_string_exception.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/widget/editable_text_widget.dart';

class GeneralSettingPage extends StatefulWidget {
  GeneralSettingPage() {}

  _GeneralSettingPageState createState() => _GeneralSettingPageState();
}

class _GeneralSettingPageState extends State<GeneralSettingPage> {
  bool? _newWordFirst = TypeSerialUtil.stringToBoolStrict(SystemConfig.getUserProperty(SystemConst.P_NEW_WOWRD_FIRST));
  bool? _defaultSilentMode = TypeSerialUtil.stringToBoolStrict(SystemConfig.getUserProperty(SystemConst.P_DEFAULT_SILENT_MODE));
  bool? _autoDownloadSound = TypeSerialUtil.stringToBoolStrict(SystemConfig.getUserProperty(SystemConst.P_AUTO_DOWNLOAD_SOUND));
  String? _VoiceDownloadUrl = SystemConfig.getUserProperty(SystemConst.P_VOICE_DOWNLOAD_URL);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocStr.of(context)!.generalSetting!),
      ),
      body: ListView(
        children: <Widget>[
          SwitchListTile(
            title: Text(LocStr.of(context)!.newWordFirst),
            value: _newWordFirst??true,
            onChanged: (bool value) async {
              await updateSystemProperties(SystemConst.P_NEW_WOWRD_FIRST, value.toString());
              setState(() {
                _newWordFirst = value;
              });
            },
          ),
          SwitchListTile(
            title: Text(LocStr.of(context)!.defaultToMute!),
            value: _defaultSilentMode??true,
            onChanged: (bool value) async {
              await updateSystemProperties(SystemConst.P_DEFAULT_SILENT_MODE, value.toString());
              setState(() {
                _defaultSilentMode = value;
              });
            },
          ),
          ExpansionTile(
            title: Text(LocStr.of(context)!.downloadVoice),
            children: <Widget>[
              SwitchListTile(
                title: Text(LocStr.of(context)!.autoDownloadVoice),
                value: _autoDownloadSound??true,
                onChanged: (bool value) async {
                  await updateSystemProperties(SystemConst.P_AUTO_DOWNLOAD_SOUND, value.toString());
                  setState(() {
                    _autoDownloadSound = value;
                  });
                },
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    LocStr.of(context)!.downloadVoiceUrlHint("\${word}"),
                    style: TextStyle(color: Colors.black54),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(20.0, 0.0, 20.0, 20.0), // Change the value as needed
                child: EditableTextWidget(
                  initText: _VoiceDownloadUrl??"",
                  maxlines: 3,
                  confirmFunc: (value) async {
                    //check if value contain '${word}'
                    if (value.contains('\${word}'))
                      return await updateSystemProperties(SystemConst.P_VOICE_DOWNLOAD_URL, value);
                    else {
                      ToastUtil.showToast(LocStr.of(context)!.invalidDownloadUrl("\${word}"));
                      return false;
                    }
                  },
                ),
              ),
            ],
          ),
          ExpansionTile(
            title: Text(LocStr.of(context)!.recallPeriod!),
            children: <Widget>[
              Padding(
                padding: EdgeInsets.all(20.0), // Change the value as needed

                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocStr.of(context)!.recallPeriodHint!,
                        style: TextStyle(color: Colors.purple),
                      ),
                      SizedBox(height: 20,),
                      Text(
                        LocStr.of(context)!.initReciteSet!,
                        style: TextStyle(color: Colors.black54),
                      ),
                      EditableTextWidget(
                        initText: SystemConfig.studyEbbinghausPoint!.pointsStr,
                        maxlines: 3,
                        confirmFunc: (value) async {
                          return await confirmEbbinghausPoint(
                              value, SystemConfig.studyEbbinghausPoint);
                        },
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Text(
                        LocStr.of(context)!.reviewIntervalSet!,
                        style: TextStyle(color: Colors.black54),
                      ),
                      EditableTextWidget(
                        initText: SystemConfig.repeatEbbinghausPoint!.pointsStr,
                        maxlines: 20,
                        confirmFunc: (value) async {
                          return await confirmEbbinghausPoint(
                              value, SystemConfig.repeatEbbinghausPoint);
                        },
                      ),
                    ]),
              ),
            ],
          )
        ],
      ),
    );
  }

  Future<bool> updateSystemProperties(String name, String value) async {
    return await SystemBiz.shared.upSertSystemProperties(name, value, SystemConfig.user!.id);
  }

  Future<bool> confirmEbbinghausPoint(
      String value, EbbinghausPoint? ebbinghausPoint) async {
    EbbinghausPoint param;
    if (ebbinghausPoint == null) {
      param = EbbinghausPoint();
      param.userId = SystemConfig.user!.id;
    } else
      param = ebbinghausPoint;

    param.pointsStr = value;
    param.id = ebbinghausPoint!.id;
    param.pointType = EbbinghausPointType.Repeat;

    bool result;
    String msg = "";

    try {
      result = await SystemBiz.shared.modifyEbbinghausPoint(param);
    } on InvalidEbbPointStringException catch (e) {
      result = false;
      msg = e.message!;
    }
    if (result == false)
      ToastUtil.showToast(LocStr.of(context)!.modifyFailedCheck(msg));
    else
      ToastUtil.showToast(LocStr.of(context)!.modifySuccess);

    return result;
  }


}
