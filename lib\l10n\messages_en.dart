// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(
    String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  static m0(word) => "Really want to delete the voice file of ${word}?";

  static m1(number) => "Move the bookmark to ${number}?";

  static m2(placehold) => "URL for download the audios, be sure contain the placeholder: ${placehold}.";

  static m3(placehold) => "Invalid URL, the URL must contain the ${placehold} ";

  static m4(msg) => "Modification failed, please check ${msg} ";

  static m5(number) => "${number} new words have been added.?";

  static m6(word) => "Deleting the study record of \"${word}\" will erase learning history for this word!";

  @override
  final Map<String, dynamic> messages = _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
      'addNewWord': MessageLookupByLibrary.simpleMessage('Add New Word'),
    'addSuccess': MessageLookupByLibrary.simpleMessage('Added successfully!'),
    'autoDownloadVoice': MessageLookupByLibrary.simpleMessage('Auto download voice'),
    'book': MessageLookupByLibrary.simpleMessage('Book'),
    'bookmark': MessageLookupByLibrary.simpleMessage('bookmark'),
    'bookmarkNotAdded': MessageLookupByLibrary.simpleMessage('This book does not have any bookmarks yet. Press and hold the bookmark icon to add a bookmark.'),
    'cancel': MessageLookupByLibrary.simpleMessage('Cancel'),
    'cantRelateToItself': MessageLookupByLibrary.simpleMessage('Cannot relate the word with itself.'),
    'click': MessageLookupByLibrary.simpleMessage('Click'),
    'clickToBackup': MessageLookupByLibrary.simpleMessage('Click here to back up by sharing.'),
    'close': MessageLookupByLibrary.simpleMessage('Close'),
    'confirmDeleteWordVoice': m0,
    'confirmMoveBookmark': m1,
    'defaultToMute': MessageLookupByLibrary.simpleMessage('Mute on startup'),
    'deletedSuccessfully': MessageLookupByLibrary.simpleMessage('Deleted Successfully!'),
    'deletionFailed': MessageLookupByLibrary.simpleMessage('Deletion failed, please try again!'),
    'details': MessageLookupByLibrary.simpleMessage('Details'),
    'dictionary': MessageLookupByLibrary.simpleMessage('Dictionary'),
    'downloadVoice': MessageLookupByLibrary.simpleMessage('Download voice'),
    'downloadVoiceUrlHint': m2,
    'editWord': MessageLookupByLibrary.simpleMessage('Edit Word'),
    'fileNotExists': MessageLookupByLibrary.simpleMessage('File not exists!'),
    'forgot': MessageLookupByLibrary.simpleMessage('Forgot'),
    'generalSetting': MessageLookupByLibrary.simpleMessage('General Setting'),
    'help': MessageLookupByLibrary.simpleMessage('Help'),
    'hint': MessageLookupByLibrary.simpleMessage('Hint'),
    'incorrectInput': MessageLookupByLibrary.simpleMessage('Incorrect Input'),
    'initReciteSet': MessageLookupByLibrary.simpleMessage('Initial Recitation Period Settings'),
    'inputDeleteToDelete': MessageLookupByLibrary.simpleMessage('Input \"delete\" to confirm.'),
    'inputToConfirmRestore': MessageLookupByLibrary.simpleMessage('Input \"restore\" to confirm.'),
    'invalidDownloadUrl': m3,
    'invalidWordOrMeaning': MessageLookupByLibrary.simpleMessage('Invalid word or meaning!'),
    'meaning': MessageLookupByLibrary.simpleMessage('meaning'),
    'modifyFailedCheck': m4,
    'modifySuccess': MessageLookupByLibrary.simpleMessage('Modification successful.'),
    'nWordAdded': m5,
    'newWordFirst': MessageLookupByLibrary.simpleMessage('New words first'),
    'noNewWordsToAdd': MessageLookupByLibrary.simpleMessage('No new words available to add!'),
    'noWordBeingStudied': MessageLookupByLibrary.simpleMessage('There are no words currently being studied!'),
    'ok': MessageLookupByLibrary.simpleMessage('Ok'),
    'recallPeriod': MessageLookupByLibrary.simpleMessage('Recall Period'),
    'recallPeriodHint': MessageLookupByLibrary.simpleMessage('If you are not familiar with the meaning of this setting, please do not modify the configuration.'),
    'relate': MessageLookupByLibrary.simpleMessage('Relate'),
    'remember': MessageLookupByLibrary.simpleMessage('Remember'),
    'restartNow': MessageLookupByLibrary.simpleMessage('Restart Now'),
    'restoreData': MessageLookupByLibrary.simpleMessage('Restore Data'),
    'restoreFailCopyError': MessageLookupByLibrary.simpleMessage('Restoration failed: Error in copying data, please restore again.'),
    'restoreFailInvalidFile': MessageLookupByLibrary.simpleMessage('Restoration failed: Invalid data file, please check the data file.'),
    'restoreFailNoFile': MessageLookupByLibrary.simpleMessage('Restoration failed: No shared data file received.'),
    'restoreFailPlsRetry': MessageLookupByLibrary.simpleMessage('Restoration failed, please restore again.'),
    'restoreSuccess': MessageLookupByLibrary.simpleMessage('Restoration successful! Please restart the app.'),
    'restoreWarning': MessageLookupByLibrary.simpleMessage('Warning: Restoring data will overwrite your existing study records! Please make sure to back up your original data.'),
    'reviewIntervalSet': MessageLookupByLibrary.simpleMessage('Review Interval Period Settings'),
    'setting': MessageLookupByLibrary.simpleMessage('Setting'),
    'shareToBackupData': MessageLookupByLibrary.simpleMessage('Share to backup data'),
    'study': MessageLookupByLibrary.simpleMessage('Study'),
    'studyRecordUpdated': MessageLookupByLibrary.simpleMessage('The study record updated.'),
    'title': MessageLookupByLibrary.simpleMessage('Welcom to Words'),
    'uncertain': MessageLookupByLibrary.simpleMessage('Uncertain'),
    'warnDeleteWordStudyRecord': m6,
    'warning': MessageLookupByLibrary.simpleMessage('Warning'),
    'word': MessageLookupByLibrary.simpleMessage('word'),
    'wordNotInDict': MessageLookupByLibrary.simpleMessage('The word is not in the dictionary, please add it to the dictionary first.'),
    'wordNotStarted': MessageLookupByLibrary.simpleMessage('The word has not started being studied yet.'),
    'wordStartedCantDelete': MessageLookupByLibrary.simpleMessage('The word has started being studied and cannot be deleted.')
  };
}
