import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'package:words/commonSupport/utils/media_utils.dart';
import 'package:words/domain/word.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/widget/add_word_to_dict_dialog.dart';
import 'package:words/ui/widget/com_add_word_rel_dialog.dart';
import 'package:words/ui/widget/dialog_util.dart';
import 'package:words/ui/widget/single_word_dialog/single_word_btn_callable.dart';

import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/utils/tts/tts_utils.dart';

import '../../../biz/word_biz.dart';
import '../../../commonSupport/call_back_interface.dart';
import 'common_single_word_btn_callback.dart';

class SingleWordDialog extends StatefulWidget {
  Word currentWord;
  WordDto? viewWordDto; //用于回调时，传回给回调，更新界面

  CurStudyRecord? currentStudyRecord;

  SingleWordBtnCallable? singleWordBtnCallable;

  List<int>? touchLocation = [];

  CallBackInterface<Map<String, Object>, Map<String, Object>>? parentCallBack;

  SingleWordDialog(this.currentWord, this.singleWordBtnCallable);
  SingleWordDialog.withParam3(this.currentWord, this.currentStudyRecord, this.singleWordBtnCallable);

  _SingleWordDialogState createState() {
    _SingleWordDialogState stat =  _SingleWordDialogState(this.currentWord, this.currentStudyRecord, this.singleWordBtnCallable!);
    return stat;
    }


}

class _SingleWordDialogState extends State<SingleWordDialog> {
  bool _isExecuted = false;
  Word? currentWord;
  CurStudyRecord? currentStudyRecord;
  StudyRecord? studyRecord;

  late SingleWordBtnCallable singleWordBtnCallable;
  bool wordChanged = false;

  HomeCubit? _homeCubit;

  final GlobalKey _transKey = GlobalKey();
  final double _buttonWidth = 100;
  final double _buttonHeight = 30;
  final double _offsetHeight = 150;
  final double _wordContentHeight = 100;

  final ScrollController _scrollController = ScrollController();

  _SingleWordDialogState(Word word, CurStudyRecord? curStudyRecord, SingleWordBtnCallable singleWordBtnCallable){

    this.currentWord = word;
    this.currentStudyRecord = curStudyRecord;
    this.singleWordBtnCallable = singleWordBtnCallable;

  }

  _init () async {
    await buildDisplayFullWord(this.currentWord!);
  }
  _init_tmp () async {
  }

  @override
  void initState() {
    super.initState();

  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isExecuted) {
        _init().then((value) {
          _homeCubit!.showByCurStep();
        });
        _isExecuted = true;
      }
    });
    return BlocProvider(
      create: (_) => HomeCubit()..showRelationStep = 0..forceEdit = false..curShowStep = 2..silentMode=false,
      child: BlocBuilder<HomeCubit, HomeState>(
        builder: (ctx, state) {
          _homeCubit = BlocProvider.of<HomeCubit>(ctx);
          _homeCubit!.currentWord = this.currentWord;
          _homeCubit!.currentStudyRecord = this.currentStudyRecord;

          return Container(
            height: MediaQuery
                .of(context)
                .size
                .height - _offsetHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
    
            child: SingleChildScrollView(
              child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                _topWordView(),
                _middleScrollView(state),
                Divider(),
                relatedWordsContent(state),

                _bottomOperationView(),
              ],
            ),


          )
          );
        },
      ),
    );
  }

  _topWordView(){
    return Container(
      height: _wordContentHeight+16,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          GestureDetector(
            onTap: (){
              _homeCubit!.playWordSound(_homeCubit!.currentWord!);
            },
            child: Container(
              decoration: BoxDecoration(
                  color: (_homeCubit!.viewCurStudyRecord!.noUse == 0) ? Colors.lightGreen[400]
                      : ((_homeCubit!.viewCurStudyRecord!.noUse == 1) ? Colors.blue
                      : ((_homeCubit!.viewCurStudyRecord!.noUse == 2) ? Colors.yellow[300]
                      : ((_homeCubit!.viewCurStudyRecord!.noUse == 3) ? Colors.red[200]
                      : Colors.grey))),
                  borderRadius: BorderRadius.circular(10)
              ),
              height: _wordContentHeight,
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 15,
                    child: _wordContent(),
                  ),
                  Positioned(
                    top: 5,
                    right: 5,
                    child:
                    GestureDetector(
                      onLongPress: (){
                        _showEditWordDialog(context);
                      },
                      child: _homeCubit!.viewWord!.editable??false ? Icon(Icons.edit, color: Colors.black45,) : Container(),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    child: _wordFooter(),
                  )
                ],
              ),
            ),
          ),
          Divider(),
        ],
      ),
    );
  }

  _wordFooter() {
    return FutureBuilder(
      future: _init_tmp(),
      builder: (BuildContext context, AsyncSnapshot snapshot){
        if (snapshot.connectionState == ConnectionState.done) {
          return Container(
            height: 30,
            child: Row(
              children: [
                SizedBox(width: 5,),
                _wordFooterItem(_homeCubit!.viewCurStudyRecord!.studyLevel.toString(), Colors.yellow[600]),
                SizedBox(width: 5,),
                _wordFooterItem(_homeCubit!.viewCurStudyRecord!.curEbbinghausStr??"0", Colors.lightBlue[200]),
                SizedBox(width: 5,),
                _wordFooterItem(_homeCubit!.viewCurStudyRecord!.curStudyLevel.toString(), ((_homeCubit!.viewCurStudyRecord!.firstJudge == null) ? false : (_homeCubit!.viewCurStudyRecord!.firstJudge == false)) ? Color.fromARGB(255, 255, 167, 159) : Colors.lightGreen[200] ),
                SizedBox(width: 5,),
                _wordFooterItem(_homeCubit!.viewCurStudyRecord!.noUse != 1 ? (_homeCubit!.wordNextStudyTime! + (_homeCubit!.viewCurStudyRecord!.noUse != 1 ? " / " : "")  + _homeCubit!.wordLastStudyTime!) : "", Colors.transparent),
              ],
            ),
          );
        } else {
          return Center(
            child: Container(),
          );
        }
      }
    );
  }

  _wordFooterItem(String str, Color? bgcolor){
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: bgcolor,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(
        str,
        style: TextStyle(
            color: Colors.black87,
            fontSize: 13
        ),
      ),
    );
  }

  _wordContent(){
    return Container(
      child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width - 140,
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child:
                SelectableText(
                    _homeCubit!.viewWord!.word!.word!,
                    style: TextStyle(
                        fontSize: 30,
                        color: Colors.black
                    ),
                    onTap: (){
                      _homeCubit!.playWordSound(_homeCubit!.currentWord!);
                    },
                  ),
              ),
            ),
          GestureDetector(
              onLongPress: () {
                DialogUtil.deleteWordSoundDialog(context,_homeCubit!.currentWord!.word!);
              },
              child: IconButton(
                  alignment: Alignment.centerLeft,
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  onPressed: (){
                    WordBiz.shared.playWordSound(_homeCubit!.currentWord!.word!);
                  },
                  icon: Icon(Icons.keyboard_voice, color: Colors.blue[800],)
              ),
            ),
          ]
      ),
    );
  }

  _middleScrollView(state) {
    return Container(
      height: 200,
      child: SingleChildScrollView(
        child: Container(
          height: 200,
          child: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                  height: 180,
                  child: wordTranslation(),
              ),
            ),
            Positioned(
              right: 15,
              bottom: 5,
              child: Container(
                height: 30,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    state.showRelationStep == 1 ? GestureDetector(
                      onTap: (){
                        _homeCubit!.showRelationStep = 0;
                        _homeCubit!.foldConjunctive(_homeCubit!.currentWord);
                      },
                      child: Icon(Icons.unfold_less_sharp, color: Colors.black45,),
                    ) : GestureDetector(
                      onTap: (){
                        _homeCubit!.showRelationStep = 1;
                        _homeCubit!.unFoldConjunctive(_homeCubit!.currentWord);
                      },
                      child: Icon(Icons.unfold_more_sharp, color: Colors.black45,),
                    ),

                    SizedBox(width: 15,),
                    GestureDetector(
                      onLongPress: (){
                        _addConjunctive(_homeCubit!.currentWord!, null);
                      },
                      onTap: (){
                        _homeCubit!.addWordRelationBtnClicked();
                      },
                      child: Container(
                          height: 40,
                          width: 40,
                          child: Icon(Icons.insert_link, color: Colors.black45,)
                      ),
                    ),
                    SizedBox(width: 10,)
                  ],
                ),
              ),
            ),
          ],
        ),

        ),

      ),
    );
  }

  // 翻译
  wordTranslation(){
    return Padding(
      key: _transKey,
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: GestureDetector (
        onTap: (){
          _homeCubit!.playWordSound(_homeCubit!.currentWord!);
        },
        child: Container(
          decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(5)
          ),
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width,
          child: SelectableText(
            _homeCubit!.viewWord!.word!.meaning!,
            style: TextStyle(
                fontSize: 15,
                color: Colors.black
            ),
            onTap: (){
              _homeCubit!.playWordSound(_homeCubit!.currentWord!);
            },
          ),
        ),
      )
    );
  }


  relatedWordsContent(state) {
    return Container(
        height: 200,
        child: FutureBuilder(
          future: _relatedWordsContent(),
          builder: (BuildContext context, AsyncSnapshot snapshot){
            if (snapshot.connectionState == ConnectionState.done) {
                    return ListView.builder(
                        controller: _scrollController,
                        itemCount: state.relatedWordList!.length,
                        padding: EdgeInsets.only(top: 8),
                        itemBuilder: (ctx, index) {
                          return GestureDetector(
                            onDoubleTapDown: (TapDownDetails details) {
                              _homeCubit!.playWordSound(state.relatedWordList![index].word!);
                              final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
                              
                              // 获取屏幕宽度
                              final double screenWidth = overlay.size.width;
                              // 假设菜单宽度为150，你可以根据实际情况调整这个值
                              const double menuWidth = 80;
                              // 检查右边是否有足够空间
                              final bool showOnLeft = (screenWidth - details.globalPosition.dx) < menuWidth;
                              
                              // 如果右边空间不足，向左偏移菜单宽度
                              final double adjustedX = showOnLeft 
                                  ? details.globalPosition.dx - menuWidth 
                                  : details.globalPosition.dx;
                                  
                              showMenu(
                                context: context,
                                position: RelativeRect.fromRect(
                                  Offset(adjustedX, details.globalPosition.dy) & Size(40, 40),
                                  Offset.zero & overlay.size,
                                ),
                                items: <PopupMenuEntry>[
                                  PopupMenuItem(
                                    padding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                                    enabled: false,
                                    height: 20,
                                    child: Container (
                                      height: 20,
                                      alignment: Alignment.center,
                                      child: Text(
                                        state!.relatedWordList![index].word!.word!,
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  PopupMenuDivider(),
                                  PopupMenuItem(
                                    child: Text(LocStr.of(context)!.details!,),
                                    value: "details",
                                    onTap: (){
                                      _showDetail(state!.relatedWordList![index].word!);
                                    },
                                  ),
                                  PopupMenuItem(
                                    child: Text(LocStr.of(context)!.relate!,),
                                    value: "association",
                                    onTap: (){
                                      _addConjunctive(_homeCubit!.currentWord!, state!.relatedWordList![index].word!.word);
                                    },
                                  ),
                                ],
                              ).then((value) {
                                if (value != null) {
                                  // Handle the selected value
                                  if (value == "details") {
                                    // Handle details action
                                  } else if (value == "association") {
                                    // Handle association action
                                  }
                                }
                              });
                            },
                            onTap: () {
                              _homeCubit!.playWordSound(
                                  state.relatedWordList![index].word!);
                            },
                            onLongPress: () {
                              _addConjunctive(_homeCubit!.currentWord!, state!.relatedWordList![index].word!.word);
                            },
                            child: Column(
                                children: [
                                  Container(
                                    color: state.relatedWordList![index]
                                        .relateStep == 0
                                        ? Colors.greenAccent[100]
                                        : Colors.transparent,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10),
                                    height: 22,
                                    child: Row(
                                      children: [
                                        Text(state.relatedWordList![index].word!
                                            .word!),
                                        SizedBox(width: 10,),
                                        Expanded(
                                          child: Text(
                                            state.relatedWordList![index].word!
                                                .meaning!,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10,),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 2,)
                                ]
                            ),
                          );
                        }
                    );

            } else
              return Center(
                child: Container(),
              );
          }
        )
    );
  }

  _relatedWordsContent() async {
    await searchRelatedWord();
    _homeCubit!.curRelatedWords = _homeCubit!.curRelatedWords == null ? [] : _homeCubit!.curRelatedWords!;
    _homeCubit!.state.relatedWordList = _homeCubit!.curRelatedWords??[];
  }

  _showDetail(Word entity){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10)
            ),
            child: SingleWordDialog(entity, CommonSingleWordBtnCallback()),
          );
        }
    );
  }

  // 30 + 32 + 40 = 102
  _bottomOperationView() {
    return ValueListenableBuilder<int>(
      valueListenable: _homeCubit!.bottomOperationViewNotifier,
      builder: (context, value, child) {
        return Container(
          height: 120,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Divider(),
              Container(
                height: 30,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTapDown: (details) {
                        _homeCubit!.color0 = Colors.grey;
                        _homeCubit!.bottomOperationViewNotifier.value++;
                      },
                      onTapUp: (details) {
                        _homeCubit!.color0 = Colors.green;
                        _homeCubit!.bottomOperationViewNotifier.value--;
                      },
                      onTapCancel: () {
                        _homeCubit!.color0 = Colors.green;
                        _homeCubit!.bottomOperationViewNotifier.value--;
                      },
                      child: Container(
                        width: _buttonWidth,
                        height: _buttonHeight,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: _homeCubit!.color0,
                        ),
                        child: Center(
                          child: Text(
                            LocStr.of(context)!.remember!,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14
                            ),
                          ),
                        ),
                      ),
                      onTap: () {
                        remberedBtn();
                      },
                    ),
                    GestureDetector(
                      onTapDown: (details) {
                        _homeCubit!.color1 = Colors.grey;
                        _homeCubit!.bottomOperationViewNotifier.value++;

                      },
                      onTapUp: (details) {
                        _homeCubit!.color1 = Colors.blue;
                        _homeCubit!.bottomOperationViewNotifier.value--;

                      },
                      onTapCancel: () {
                        _homeCubit!.color1 = Colors.blue;
                        _homeCubit!.bottomOperationViewNotifier.value--;

                      },
                      child: Container(
                        width: _buttonWidth,
                        height: _buttonHeight,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: _homeCubit!.color1,
                        ),
                        child: Center(
                          child: Text(
                            LocStr.of(context)!.uncertain!,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14
                            ),
                          ),
                        ),
                      ),
                      onTap: () {
                        blurryBtn();
                      },
                    ),
                    GestureDetector(
                      onTapDown: (details) {
                        _homeCubit!.color2 = Colors.grey;
                        _homeCubit!.bottomOperationViewNotifier.value++;

                      },
                      onTapUp: (details) {
                        _homeCubit!.color2 = Colors.red[400]!;
                        _homeCubit!.bottomOperationViewNotifier.value--;

                      },
                      onTapCancel: () {
                        _homeCubit!.color2 = Colors.red[400]!;
                        _homeCubit!.bottomOperationViewNotifier.value--;

                      },
                      child: Container(
                        width: _buttonWidth,
                        height: _buttonHeight,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: _homeCubit!.color2,
                        ),
                        child: Center(
                          child: Text(
                            LocStr.of(context)!.forgot!,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14
                            ),
                          ),
                        ),
                      ),
                      onTap: () {
                        forgetBtn();
                      },
                    ),
                  ],
                ),
              ),
              Divider(),
              SizedBox(height: 10,),
              Container(
                width: 150,
                height: 30,
                child: TextButton(
                    style: ButtonStyle(
                        padding: MaterialStateProperty.all(EdgeInsets.zero)
                    ),
                    onPressed: () {
                      singleWordDialogCancel();
                    },
                    child: Text(LocStr.of(context)!.close!)
                ),
              )
            ],
          ),
        );
    }
  );


  }

  _addConjunctive(Word entity, String? searchStr){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: ComAddWordRelDialog(entity, searchStr, (){
              _homeCubit!.conjunctiveReload(_homeCubit!.currentWord);
            }),
          );
        }
    );
  }

  void singleWordDialogCancel() {
    //如果词的内容改变了，父窗口回调也不为空，就回调
    if (widget.parentCallBack != null) {
      Map<String, Object> param = {};
      bool tWordChanged = wordChanged;
      param["wordChanged"] = tWordChanged;
      param["sourceWordDto"] = widget.viewWordDto!;
      widget.parentCallBack!.process(param);
    }
    cleanVMEnv();
    Navigator.of(context).pop();
  }

  void remberedBtn() {
    processRemberWord();
    singleWordDialogCancel();
  }

  void forgetBtn() {
    processForgetWord();
    singleWordDialogCancel();
  }

  void blurryBtn() {
    processBlurryWord();
    singleWordDialogCancel();
  }


  void processRemberWord(){
    if (this.studyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotStarted);
      return;
    }
    singleWordBtnCallable.processRemberBtn(currentStudyRecord!);
  }

  void processForgetWord() {
    if (this.studyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotStarted);
      return;
    }
    singleWordBtnCallable.processForgetBtn(currentStudyRecord!);
    ToastUtil.showToast(LocStr.of(context)!.studyRecordUpdated);
  }

  void processBlurryWord() {
    if (this.studyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotStarted);
      return;
    }
    singleWordBtnCallable.processBlurryBtn(currentStudyRecord!);
  }

  Future<void> buildDisplayFullWord(Word word) async {
    if (this.studyRecord == null)
      this.studyRecord = await WordBiz.shared.queryStudyRecordByContendId(word.id);

    if (this.currentStudyRecord == null)
      this.currentStudyRecord = await WordBiz.shared.queryCurStudyRcdByContendId(word.id);

    if ((this.currentStudyRecord == null) && (this.studyRecord != null)){
      this.currentStudyRecord = CurStudyRecord.fromRecord(this.studyRecord!);
    }

    _homeCubit!.currentStudyRecord = this.currentStudyRecord;
    _homeCubit!.calCurNextTimeStr(); //计算显示时间字串

    completeShowWord();
  }

  searchRelatedWord() async {
    _homeCubit!.curRelatedWords = await WordBiz.shared.searchRelatedWord(_homeCubit!.currentWord, _homeCubit!.showRelationStep!);
  }

  void completeShowWord() {
    if (currentWord == null) {
      _homeCubit!.viewWord!.word!.word = "";
      _homeCubit!.viewWord!.word!.meaning = "";
      _homeCubit!.viewCurStudyRecord!.studyLevel = 0;
      _homeCubit!.viewCurStudyRecord!.curStudyLevel = 0;
    } else {
      _homeCubit!.viewWord!.word!.word = currentWord!.word;
      _homeCubit!.viewWord!.word!.meaning = currentWord!.meaning;
      _homeCubit!.viewWord!.word!.ownerId = currentWord!.ownerId;
      _homeCubit!.viewWord!.editable = WordDto.calEditableTool(currentWord, _homeCubit!.forceEdit);

      if (currentStudyRecord != null) {
        WordBiz.shared.copyCommShowValue(currentStudyRecord, _homeCubit!.viewCurStudyRecord);
      } else {
        _homeCubit!.viewCurStudyRecord!.studyLevel = 0;
        _homeCubit!.viewCurStudyRecord!.curStudyLevel = 0;
        _homeCubit!.viewCurStudyRecord!.curEbbinghausStr = "0";
        _homeCubit!.viewCurStudyRecord!.noUse = 1;

      }
    }
    //刷新界面
    setState(() {

    });
  }

  void cleanVMEnv() {
    _homeCubit!.forceEdit = false;
    _homeCubit!.showRelationStep = 0;
  }

  Future<void> _showEditWordDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AddWordToDictDialog(word: _homeCubit!.currentWord,
            callBack: () {
                _homeCubit!.showByCurStep();
            });
      },
    );
  }

}