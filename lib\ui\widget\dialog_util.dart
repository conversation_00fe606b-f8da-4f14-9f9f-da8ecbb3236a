import 'package:flutter/material.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/domain/word.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/l10n/loc_str.dart';

class DialogUtil {
  static void deleteWordSoundDialog(BuildContext context, String word) {
    showDialog(
      context: context,
      barrierDismissible: false, // 设置点击对话框外部不关闭对话框
      builder: (BuildContext context) {
        // 返回一个 AlertDialog
        return AlertDialog(
          title: Text(LocStr.of(context)!.hint!),
          content: Container(
              height: 80.0, // 你可以在这里设置你想要的高度
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    Text(LocStr.of(context)!.confirmDeleteWordVoice(word),
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              )
          ),
          actions: <Widget>[
            // 取消按钮
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.cancel!,),
            ),
            // 确认按钮
            TextButton(
              onPressed: () async {
                int res = await WordBiz.shared.deleteWordSound(word.toLowerCase()); //转为lowercase，因为语音文件都是用小写名存储的
                if (res == 1)
                  ToastUtil.showToast(LocStr.of(context)!.deletedSuccessfully);
                else
                  ToastUtil.showToast(LocStr.of(context)!.deletionFailed);
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.ok!),
            ),
          ],
        );
      },
    );
  }
}