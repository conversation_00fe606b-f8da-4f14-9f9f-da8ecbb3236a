import 'package:flutter/material.dart';
import 'package:words/ui/book/book_content.dart';
import 'package:words/ui/setting/general_setting_page.dart';
import 'package:words/ui/setting/help_page.dart';
import 'package:words/ui/setting/restore_setting_page.dart';
import 'package:words/ui/home/<USER>';

class WordRouter {

  static String bookContent = "/book_content";
  static String generalStatistics = "/home/<USER>";
  static String restoreSetting = "/setting/restoreSetting";
  static String generalSetting = "/setting/generalSetting";
  static String helpPage = "/setting/helpPage";

  static final routes = {
    bookContent: (context, args) => BookContent(args["book"]),
    restoreSetting: (context, args) => RestoreSettingPage(),
    generalStatistics: (context, args) => GeneralStatisticsPage(),
    generalSetting: (context, args) => GeneralSettingPage(),
    helpPage: (context, args) => HelpPage(),
  };

  static Route generateRouter(RouteSettings settings){
    final String? name = settings.name;
    final Function? builder = WordRouter.routes[name];
    if (builder == null) {
      return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(
              title: Center(
                child: Text("Error Page"),
              ),
            ),
            body: Center(child: Text("Page Not Found"),),
          ));
    }else{
      return MaterialPageRoute(
        builder: (ctx) => builder(settings.name, settings.arguments),
        settings: settings
      );
    }
  }

}