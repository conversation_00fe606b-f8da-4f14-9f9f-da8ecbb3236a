name: words
description: words app

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=2.15.0 <=3.22.3"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  json_annotation: ^4.9.0
  intl: ^0.19.0
  intl_translation: 0.20.1
  fluttertoast: 8.2.12
  cupertino_icons: ^1.0.8
  flutter_bloc: ^8.1.6
  sqflite: ^2.3.3+1
  uuid: ^4.4.2
  easy_refresh: ^3.4.0
  flutter_tts: ^4.0.2
  scrollable_positioned_list: ^0.3.8
  permission_handler: ^11.3.1
  just_audio: ^0.9.36
  share_plus: ^10.0.0
  receive_sharing_intent: ^1.8.0
  http: ^1.2.2
  path_provider: ^2.1.4
  webview_flutter: ^4.8.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.11
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following ui: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/image/
    - assets/resources/
    - assets/help/
    - assets/help/images/
  fonts:
   - family: word_not_in_dict
     fonts:
       - asset: assets/fonts/word_not_in_dict.ttf

  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for t/////he font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
