import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/widget/single_word_dialog/single_word_dialog.dart';
import 'package:words/utils/router/word_router.dart';
import 'home_cubit.dart';

class HomeFirstView extends StatefulWidget {
  HomeCubit? _homeCubit;
  HomeFirstView(this._homeCubit);
  _HomeFirstViewState createState() => _HomeFirstViewState();
}

class _HomeFirstViewState extends State<HomeFirstView> {
  final ScrollController _scrollController = ScrollController();
  _HomeFirstViewState();

  @override
  void initState() {
    _scrollController.addListener(() {

    });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(10),
      child: Container(
        height: 105,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: <BoxShadow>[
            BoxShadow(
                color: Colors.blue.withOpacity(0.2),
                offset: Offset(1.1, 1.1),
                blurRadius: 10.0),
          ],
        ),
        child: Column(
          children: [
            header(context),
            lastCurStudyRcdsContent(),
          ],
        ),
      ),
    );
  }

  header(BuildContext context){
    return Container(
      height: 30,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Material(
                color: Colors.transparent, // 设置为透明，以免影响布局颜色
                child: InkWell(
                  child: Icon(Icons.flash_on),
                  onTap: () async {
                    await studyNextWord(context);
                  },
                ),
              ),
              SizedBox(width: 5,),
              headerItem(widget._homeCubit!.remainExpiredStarted!.toString() + "/" + widget._homeCubit!.remainExpired!.toString(), Colors.lightGreen, style: 1),
              headerItem(widget._homeCubit!.remainTodayStarted!.toString() + "/" + widget._homeCubit!.remainToday!.toString(), Color.fromRGBO(255, 225, 123, 1.0), style: 2),
              SizedBox(width: 5,),
                GestureDetector(
                onLongPress: () {
                  Navigator.pushNamed(context, WordRouter.generalStatistics);
                },
                child: headerItem((widget._homeCubit!.remainCommingStarted!).toString() + "/" + (widget._homeCubit!.remainComming!).toString(), Color.fromARGB(255, 255, 167, 159)),
              ),
              SizedBox(width: 5,),
              headerItem(widget._homeCubit!.remainNewStarted!.toString() + "/" + widget._homeCubit!.remainNew!.toString(), Colors.lightBlue[300]),
              SizedBox(width: 5,),
              headerItem(widget._homeCubit!.nextTimeOfAll, Colors.transparent),
            ],
          ),
          Material(
            color: Colors.transparent, // 设置为透明，以免影响布局颜色
            child: InkWell(
              child: Icon(Icons.refresh),
              onTap: () async {
                await refreshCurStudyRecord(context);
              },
            ),
          ),
        ]
      ),
    );
  }

  headerItem(String? str, Color? bgcolor,{int style = 0}){
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: bgcolor,
        borderRadius: style == 0 ? BorderRadius.circular(5) :
          style == 1 ? BorderRadius.only(
            topLeft: Radius.circular(5),
            bottomLeft: Radius.circular(5),
          ) : BorderRadius.only(
            topRight: Radius.circular(5),
            bottomRight: Radius.circular(5),
          ),
      ),
      child: Text(
        str??"",
        style: TextStyle(
          color: Colors.black87,
          fontSize: 13
        ),
      ),
    );
  }

  lastCurStudyRcdsContent(){
    return Container(
        height: 75,
        child: _lastCurStudyRcdsContent()
    );
  }

  _lastCurStudyRcdsContent(){
    List<CurStudyRecord>? tLastCurStudyRcds = widget._homeCubit!.historyCurStudyRcdBuffer.historyCurRecords;
      tLastCurStudyRcds = tLastCurStudyRcds == null ? [] : tLastCurStudyRcds;
      if (tLastCurStudyRcds.length == 0) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.event_note_outlined, size: 40, color: Colors.grey.shade300,),
              Text("", style: TextStyle(color: Colors.grey.shade400),)
            ],
          ),
        );
      }else{
        WidgetsBinding.instance.addPostFrameCallback((_) => _scrollController.jumpTo(_scrollController.position.maxScrollExtent));
        return ListView.builder(
            controller: _scrollController,
            itemCount: tLastCurStudyRcds.length,
            padding: EdgeInsets.only(top: 0),
            itemBuilder: (ctx, index) {
              return GestureDetector(
                onTap: (){
                  widget._homeCubit!.playWordSound(tLastCurStudyRcds![index].curWord!);
                },
                onLongPress: (){
                  _lastRcdItemLongClicked(tLastCurStudyRcds![index]);
                },
                child: Container(
                  color: Colors.transparent,
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  height: 25,
                  child: Row(
                    children: [
                      Text(tLastCurStudyRcds![index].curWord!.word!),
                      SizedBox(width: 10,),
                      Expanded(
                        child: Text(
                          tLastCurStudyRcds[index].curWord!.meaning!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey
                          ),
                        ),
                      ),
                      SizedBox(width: 10,),
                    ],
                  ),
                ),
              );
            }
        );
      }

  }

  _lastRcdItemLongClicked(CurStudyRecord vCurStudyRcd){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10)
            ),
            child: _buildSingleWordDialog(vCurStudyRcd),
          );
        }
    );
  }
  _buildSingleWordDialog(CurStudyRecord vCurStudyRcd) {
    var result = SingleWordDialog.withParam3(vCurStudyRcd.curWord!, vCurStudyRcd.silentClone(), ReDoSingleWordBtnCallback());
    return result;
  }

  refreshCurStudyRecord(BuildContext context) async {
    int addRes = await widget._homeCubit!.addAllCurStudyRecord();
    if (addRes == 0)
      ToastUtil.showToast(LocStr.of(context)!.noNewWordsToAdd);
    else {
      await widget._homeCubit!.refreshBuffer();

      await widget._homeCubit!.calRemainCurRecord();
      ToastUtil.showToast(LocStr.of(context)!.nWordAdded(addRes.toString()));
    }


    await widget._homeCubit!.calNextEffectStartTime(SystemConfig.user!.id, DateTime.now());

    widget._homeCubit!.syncHeadDataView();

    await widget._homeCubit!.studyNextWord(context);

    return;
  }

  Future<void> studyNextWord(BuildContext context) async {
    await widget._homeCubit!.studyNextWord(context);
  }
}

