import 'package:flutter/material.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/domain/word.dart';


class AddWordToDictDialog extends StatefulWidget {
  Function? callBack;
  String? wordStr;
  String? meaningStr;
  Word? word;

  AddWordToDictDialog({this.word, this.wordStr, this.meaningStr, this.callBack}) {
    if (this.word != null) {
      this.wordStr = this.word!.word;
      this.meaningStr = this.word!.meaning;
    }
  }

  _AddWordToDictDialogState createState() {
    _AddWordToDictDialogState res = _AddWordToDictDialogState();
    return res;
  }
}

class _AddWordToDictDialogState extends State<AddWordToDictDialog> {

  TextEditingController _addWordFieldController = TextEditingController();
  TextEditingController _addMeaningFieldController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _addWordFieldController.text = widget.wordStr == null ? "" : widget.wordStr!.trim();
    _addMeaningFieldController.text = widget.meaningStr== null ? "" : widget.meaningStr!.trim();
  }

  @override
  void dispose() {
    _addWordFieldController.dispose();
    _addMeaningFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        child: PopScope(
          canPop: true, // 当为 false 时，阻止当前路由被弹出
          onPopInvoked: (didPop) {
            // 在这里添加你的清理操作
            _addWordFieldController.clear();
            _addMeaningFieldController.clear();
            return;
          },
          child: AlertDialog(
            title: Text(widget.word == null ? LocStr.of(context)!.addNewWord : LocStr.of(context)!.editWord,
              style: TextStyle(
                fontSize: 20, // 设置字体大小为20
              ),
            ),
            content: Container(
                height: 300.0, // 你可以在这里设置你想要的高度
                child: SingleChildScrollView(
                  child: Column(
                    children: <Widget>[
                      TextField(
                        controller: _addWordFieldController,
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                          hintText: LocStr.of(context)!.word,
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 15),
                      TextField(
                        controller: _addMeaningFieldController,
                        maxLines: 10,
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                          hintText: LocStr.of(context)!.meaning,
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                )
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  _addWordFieldController.clear();
                  _addMeaningFieldController.clear();
                  Navigator.of(context).pop(); // 关闭对话框
                },
                child: Text(LocStr.of(context)!.cancel),
              ),
              TextButton(
                onPressed: () async {
                  int res = await addWord2DickConfirm();
                  if (res == 1) {
                    _addWordFieldController.clear();
                    _addMeaningFieldController.clear();
                    Navigator.of(context).pop(); // 关闭对话框
                  }
                },
                child: Text(LocStr.of(context)!.ok),
              ),

            ],
          ),
        ),

    );
  }

  Future<int> addWord2DickConfirm() async {
    Word newWord = Word()
      ..word = _addWordFieldController.text
      ..meaning = _addMeaningFieldController.text
      ..ownerId = SystemConfig.user!.id; //单词如果修改了，owner都是用户

    if (widget.word != null)
      newWord.id = widget.word!.id;

    newWord.word = newWord.word?.trim();
    if (StringUtil.isEmpty(newWord.word) || StringUtil.isEmpty(newWord.meaning)) {
      ToastUtil.showToast(LocStr.of(context)!.invalidWordOrMeaning);
      return -1;
    }

    String showTxt = LocStr.of(context)!.modifySuccess;

    if (StringUtil.isEmpty(newWord.id)) { //Id为空，则插入，不为空则修改
      newWord.frequency = 1;
      newWord.ownerId = SystemConfig.user!.id;
      showTxt = LocStr.of(context)!.addSuccess;
    }
    newWord.userEdited = true;

    newWord.searchId = newWord.word!.toLowerCase();
    await updateInsertWord(newWord);

    if (widget.word != null) {
      widget.word!.word = newWord.word;
      widget.word!.meaning = newWord.meaning;
      widget.word!.searchId = newWord.searchId;
    }
    ToastUtil.showToast(showTxt);

    if (widget.callBack != null)
      widget.callBack!();

    return 1;
  }

  updateInsertWord(Word newWord) async {
    return await WordBiz.shared.updateInsertWord(newWord);
  }
}
