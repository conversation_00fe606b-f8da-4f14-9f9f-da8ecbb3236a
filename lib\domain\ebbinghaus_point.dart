import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:words/domain/support/has_string_properties.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'dart:convert';
import 'package:words/exception/invalid_ebb_point_string_exception.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';

part 'ebbinghaus_point.g.dart';

@JsonSerializable()
class EbbinghausPoint implements HasStringProperties{
  @Json<PERSON>ey(name: "ID")
  String? id;

  @<PERSON><PERSON><PERSON><PERSON>(name: "USER_ID")
  String? userId;

  @J<PERSON><PERSON><PERSON>(name: "POINT_TYPE_STR")
  String? pointTypeStr;

  @JsonKey(ignore: true)
  EbbinghausPointType? pointType;

  @J<PERSON><PERSON>ey(name: "POINTS_STR")
  String? pointsStr;

  @<PERSON><PERSON><PERSON><PERSON>(ignore: true)
  List<int>? ebbinghausPoint;

  @<PERSON><PERSON><PERSON><PERSON>(ignore: true)
  List<String>? pointStrList;

  EbbinghausPoint();

  factory EbbinghausPoint.fromJson(Map<String, dynamic> json) => _$EbbinghausPointFromJson(json);

  Map<String, dynamic> toJson() => _$EbbinghausPointToJson(this);
	@override
	String toString() {
		return jsonEncode(this);
	}

  void buildStringFromPointType() {
    if (pointType != null) {
      pointTypeStr = pointType!.value;
    } else {
      pointTypeStr = null;
    }
  }

  @override
  void buildStringFromProperty() {
    buildStringFromEbbPointStrList();
    buildStringFromPointType();
  }

  void buildStringFromEbbPointStrList() {
    if (CollectionUtil.nonEmpty(pointStrList)) {
      pointsStr = pointStrList!.join(',');
    } else {
      pointsStr = null;
    }
  }

  void buildPointTypeFromString() {
    if (StringUtil.nonEmpty(this.pointTypeStr)) {
      pointType = EbbinghausPointTypeExtensions.fromValue(pointTypeStr!);
    } else {
      pointType = null;
    }
  }

  static List<String>? buildpointStrListFromString(String? vPointStr) {
    if (StringUtil.isEmpty(vPointStr))
      return null;
    return vPointStr!.split(',');
  }

  static List<int>? buildEbbinghausPointFromStringList(List<String>? vPointStrList) {
    if (CollectionUtil.isEmpty(vPointStrList)) return null;

    final res = <int>[];
    for (var strPoint in vPointStrList!) {
      res.add(calPointFromStr(strPoint));
    }
    return res;
  }

  static bool isValidEbbinghausStr(String? vStr) {
    if (StringUtil.isEmpty(vStr))
      return false;

    List<String>? strList = buildpointStrListFromString(vStr);
    if (CollectionUtil.isEmpty(strList))
      return false;

    List<int>? reslut = buildEbbinghausPointFromStringList(strList);
    if (CollectionUtil.isEmpty(strList))
      return false;
    else
      return true;
  }
  @override
  void buildPropertyFromString() {
    buildPointTypeFromString();
    try {
      this.pointStrList = buildpointStrListFromString(this.pointsStr);
      this.ebbinghausPoint = buildEbbinghausPointFromStringList(this.pointStrList);
    } on InvalidEbbPointStringException catch(e) {
      throw e;
    }
  }

// 其他业务逻辑
  static int calPointFromStr(String strPoint) {
    if (strPoint.isEmpty) {
      throw InvalidEbbPointStringException(strPoint);
    }

    final durationStrings = strPoint.toLowerCase().split(RegExp(r"(?<=[smhd])"));

    var res = 0;

    for (var duration in durationStrings) {
      res += calOneElementFromStr(duration);
    }

    return res;
  }

  static int calOneElementFromStr(String strPoint) {
    if (strPoint.isEmpty) {
      throw InvalidEbbPointStringException(strPoint);
    }

    String measure = strPoint.trim().substring(strPoint.length - 1);
    String num = strPoint.trim().substring(0, strPoint.length - 1);

    int res;

    try {
      switch (measure.toLowerCase()) {
        case 's':
          res = int.parse(num) * 1000;
          break;
        case 'm':
          res = int.parse(num) * 60 * 1000;
          break;
        case 'h':
          res = int.parse(num) * 60 * 60 * 1000;
          break;
        case 'd':
          res = int.parse(num) * 24 * 60 * 60 * 1000;
          break;
        default:
          throw InvalidEbbPointStringException(strPoint);
      }
    } catch (err) {
      throw InvalidEbbPointStringException(strPoint);
    }
    return res;
  }
}