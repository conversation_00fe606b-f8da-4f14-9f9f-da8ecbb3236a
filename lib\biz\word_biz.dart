import 'dart:collection';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/domain/book_content.dart';
import 'package:words/domain/statistics_count_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/manager/word_manager.dart';
import 'package:words/domain/cur_study_rcd_buffer.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/word.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

import 'package:words/Manager/statistics_level_sum_manager.dart';
import 'package:words/Manager/word_relation_manager.dart';
import 'package:words/common/system_config.dart';
import 'package:words/common/system_const.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/manager/cur_study_record_manager.dart';
import 'package:words/manager/study_record_manager.dart';
import 'package:words/domain/statistics_level_sum.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/domain/word_relation.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'package:words/commonSupport/utils/media_utils.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
class WordBiz {

  factory WordBiz() => _shared();
  static WordBiz get shared => _shared();
  static WordBiz? _instance;
  WordBiz._();
  static WordBiz _shared() {
    if (_instance == null) {
      _instance = WordBiz._();
    }
    return _instance!;
  }

  wordListQueryByRelation(String iD) async {
    return await WordManager.shared.wordListQueryByRelation(iD);
  }

  deleteCurStudyRecord(CurStudyRecord? currentStudyRecord) async {
    return await CurStudyRecordManager.shared.deleteCurStudyRecord(currentStudyRecord);
  }

  deleteStudyRecordByContentId(String contentId) async {
    return await StudyRecordManager.shared.deleteStudyRecordByContentId(contentId);
  }

  Future<int?> calRemainExpiredCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainExpiredCurStudyRcd(now);
  }
  Future<int?> calRemainExpiredStartedCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainExpiredStartedCurStudyRcd(now);
  }

  Future<int?> calRemainNewCurStudyRcd(DateTime now) async {
    return await CurStudyRecordManager.shared.calRemainNewCurStudyRcd(now);
  }

  Future<int?> calRemainNewStartedCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainNewStartedCurStudyRcd(now);
  }

  Future<int?> calRemainTodayCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainTodayCurStudyRcd(now);
  }
  Future<int?> calRemainTodayStartedCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainTodayStartedCurStudyRcd(now);
  }

  Future<int?> calRemainCommingCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainCommingCurStudyRcd(now);
  }
  Future<int?> calRemainCommingStartedCurStudyRcd(DateTime now) async{
    return await CurStudyRecordManager.shared.calRemainCommingStartedCurStudyRcd(now);
  }

  Future<String?> calNextEffectStartTime(String userId, DateTime now) async {
    String resTime = "";
    String netEffStartTimeFmt = "MM.dd HH:mm:ss";

    CurStudyRecord? result;
    List<CurStudyRecord>? recordList;

    // 先找不是新词、已到期的词
    recordList = await CurStudyRecordManager.shared.getStartedExpiredRecord(userId, now);

    if (CollectionUtil.nonEmpty(recordList)) {
      result = CurStudyRecord.getMinRcdFromList(recordList!);
      resTime = DateUtil.date2String(result!.nextStudyDate, netEffStartTimeFmt);
      return resTime;
    }

    // 如果没有，查找最近的、未学过的词，即新词
    recordList = await CurStudyRecordManager.shared.queryNotStartedRecord(userId);

    if (CollectionUtil.nonEmpty(recordList)) {
      result = recordList![0];
      resTime = DateUtil.date2String(result.nextStudyDate, netEffStartTimeFmt);
      return resTime;
    }

    // 如果没有，找不是新词，已开始学，但未到期的词
    // 即将到期的，按到期时间排序，而不能用按级别排序。按级别排序用在从一批已到期的词中选。对未到期的，查看最近时间，要用时间排序
    recordList = await CurStudyRecordManager.shared.getStartedNotExpiredRecord(userId, now);

    if (CollectionUtil.nonEmpty(recordList)) {
      result = recordList![0];
      resTime = DateUtil.date2String(result.nextStudyDate, netEffStartTimeFmt);
      return resTime;
    }

    // 如果没有，则在所有学习记录中查找最近的、未到期的词
    List<StudyRecord>? studyRecordList = await StudyRecordManager.shared.getStartedNotExpiredRecord(SystemConfig.user!.id, now);

    if (CollectionUtil.nonEmpty(studyRecordList)) {
      resTime = resTime = DateUtil.date2String(studyRecordList![0].nextStudyDate, netEffStartTimeFmt);
    } else {
      resTime = "";
    }

    return resTime;
  }

  Future<CurStudyRecord?> getNextFromCurStyRcdInSequence(String userId, DateTime now) async {
    List<CurStudyRecord>? recordList = null;
    CurStudyRecord? result = null;
    CurStudyRecord? bufResult = null;

    // 先找到期、且curStudyLevel不为0，即不是新词的词
    recordList = await CurStudyRecordManager.shared.queryStartedRecord(userId, now);
    if (CollectionUtil.nonEmpty(recordList)) {
      result = CurStudyRecord.getMinRcdFromList(recordList);
      result!.noUse = 0;

      // 如果设置了先学习新词，则检查是否已开始，如果是没开始的词，则暂存本词，先看有没有新词。如果没有新词，才返回暂存的词
      if (true == result.firstJudge &&
          "true" == SystemConfig.getUserProperty(SystemConst.P_NEW_WOWRD_FIRST)) {
        bufResult = result;
      } else
        return result;
    }

    // 如果没有，查找最近的、未学过的词，即新词
    recordList = await CurStudyRecordManager.shared.queryNotStartedRecord(userId);

    if (CollectionUtil.nonEmpty(recordList)) {
      result = recordList![0];
      result.noUse = 1;
      return result;
    }

    if (bufResult != null) // 如果没有新词，之前所暂存的词有值，则返回之前暂存的词
      return bufResult;

    // 如果再没有，找即将到期的旧词
    recordList = await CurStudyRecordManager.shared.queryRemainCommingRecord(userId, now);
    if (CollectionUtil.nonEmpty(recordList)) {
      result = CurStudyRecord.getMinRcdFromList(recordList);
      if (DateUtil.isNextToday(result!.nextStudyDate!))
        result.noUse = 3;
      else
        result.noUse = 2;
      return result;
    }

    return result;
  }

  Future<Word?> queryWordByIdOfUserNSys(String contentId, String userId) async {
    return await WordManager.shared.queryWordByIdOfUserNSys(contentId, userId);
  }

  void playWordSound(String? wordSearchId) {
    if (wordSearchId == null)
      return;
    if (SystemConfig.getUserProperty(SystemConst.P_AUTO_DOWNLOAD_SOUND) == "true") {
      //下载语音文件
      downloadVoiceFile(wordSearchId);
    }

    try {
      MediaUtils.playMediaFile(wordSearchId);
    } catch (e) {
      //找不到音频是正常，无需输出
    }
  }

  Future<List<WordDto>?> searchRelatedWord(Word? currentWord, int step) async {
    if (currentWord == null) return null;

    List<WordDto>? relatedWordDtos;

    if (step == 0)
      relatedWordDtos = await query1StepRelatedWordForWord(currentWord);
    else
      relatedWordDtos = await query2StepRelatedWordForWord(currentWord);

    // Remove self
    if (CollectionUtil.nonEmpty(relatedWordDtos)) {
      for (WordDto dto in relatedWordDtos!) {
        if(dto.word!.id == currentWord.id) {
          relatedWordDtos.remove(dto);
          break;
        }
      }
    }
    return relatedWordDtos;
  }

  Future<List<Word>?> queryAllDirectRelatedWordForWord(Word? word) async {
    if (word == null) return null;

    List<WordRelation>? wordRelations = await WordRelationManager.shared.queryAllDirectRelationForWord(word);

    if (CollectionUtil.isEmpty(wordRelations)) return null;

    List<String> wordIds = [];
    for (WordRelation relation in wordRelations!) {
      String? efficientId = relation.getEfficientWordId(word.id!);
      if (StringUtil.nonEmpty(efficientId)) wordIds.add(efficientId!);
    }
    List<Word>? res = await WordManager.shared.queryWordByIdList(wordIds);
    return res;
  }

  Future<List<WordDto>?> query1StepRelatedWordForWord(Word currentWord) async {
    List<Word>? firstRelatedWords = await queryAllDirectRelatedWordForWord(currentWord);

    if (CollectionUtil.isEmpty(firstRelatedWords)) return null;

    List<WordDto> firstWordDtos = WordDto.convertToWordDtoList(firstRelatedWords)!;

    Map<String, WordDto> res = LinkedHashMap<String, WordDto>();
    for (WordDto wordDto in firstWordDtos) {
      if (res[wordDto.word!.id] == null) {
        if (wordDto.relateStep == null) {
          // 避免覆盖掉前面已置过的标记，这里判断一下
          wordDto.relateStep = 0; // 直接关联标志
        }
        if (wordDto.word!.id != currentWord.id) {
          res[wordDto.word!.id!] = wordDto;
        }
      }
    }
    return List<WordDto>.from(res.values);
  }

  Future<List<WordDto>?> query2StepRelatedWordForWord(Word currentWord) async {
    List<Word>? firstRelatedWords = await queryAllDirectRelatedWordForWord(currentWord);

    if (CollectionUtil.isEmpty(firstRelatedWords)) return null;

    List<WordDto> firstWordDtos = WordDto.convertToWordDtoList(firstRelatedWords)!;

    Map<String, WordDto> res = LinkedHashMap<String, WordDto>();
    for (WordDto wordDto in firstWordDtos) {
      if (res[wordDto.word!.id] == null) {
        if (wordDto.relateStep == null) {
          // 避免覆盖掉前面已置过的标记，这里判断一下
          wordDto.relateStep = 0; // 直接关联标志
        }
        if (wordDto.word!.id != currentWord.id) {
          res[wordDto.word!.id!] = wordDto;
        }
      }

      if (wordDto.word!.id == currentWord.id) continue;

      List<Word>? secondRelateWords = await queryAllDirectRelatedWordForWord(wordDto.word);

      if (CollectionUtil.nonEmpty(secondRelateWords)) {
        List<WordDto>? secondWordDtos = WordDto.convertToWordDtoList(secondRelateWords);
        // 从第二步的结果中删除第一步中已包含的，因为第二步关联词可能会和第一步的词重复
        WordRelationManager.shared.removeWordDtoListFromList(secondWordDtos, firstWordDtos);
        for (WordDto secWordDto in secondWordDtos!) {
          if (res[secWordDto.word!.id] == null) {
            if (secWordDto.relateStep == null) {
              // 避免覆盖掉前面已置过的标记，这里判断一下
              secWordDto.relateStep = 1; // 一步间接关联标志
            }
            res[secWordDto.word!.id!] = secWordDto;
          }
        }
      }
    }
    return List<WordDto>.from(res.values);
  }

  Future<void> refreshNoUseRcdBuffer(CurStudyRcdBuffer curStudyRcdBuffer) async {
    await CurStudyRecordManager.shared.refreshNoUseRcdBuffer(curStudyRcdBuffer);
  }

  Future<void> processRemberWord(CurStudyRecord? currentStudyRecord) async {
    DateTime now = DateTime.now();

    if (currentStudyRecord == null) {
      return;
    }

    // 如果当前单词不是noUse的词
    if (currentStudyRecord.noUse == null || currentStudyRecord.noUse != -1) {
      //如果已不在curStudyRecord中，则重新插入，如还在curStudyRecord中，则什么都不做
      await regetNoUseWordToCurRecord(currentStudyRecord);
    }

    if (currentStudyRecord.firstJudge == true) {
      // 如果是当天首次过这个单词，并且记住了，则升级
      // 升级
      if (currentStudyRecord.studyLevel! >=
          SystemConfig.repeatEbbinghausPoint!.ebbinghausPoint!.length) {
        currentStudyRecord.curAdjStudyLevel =
            SystemConst.TOP_REPEAT_LEV - currentStudyRecord.studyLevel!;
      } else {
        currentStudyRecord.curAdjStudyLevel = 1;
      }

      // 统计LevelSum
      await statisticLevelSum(currentStudyRecord, currentStudyRecord.oriStudyLevel! - 1, now, 1, 0, 0);
    }

    if (currentStudyRecord.noUse == null || currentStudyRecord.noUse != -1) {
      // 已完成的，占时间用的词，跳过修改数据
      // 如果当前时间没达到最小复习时间，则这轮不计算，但如果是双0新词，要算
      if (now.isAfter(currentStudyRecord.minNextStudyDate!)
          || (currentStudyRecord.curStudyLevel == 0 && currentStudyRecord.studyLevel == 0)) {
        // 根据当前学习是否完成，分别更新StudyRecord和CurStudyRecord
        // 如果达到了当天标准
        if (currentStudyRecord.curStudyLevel! >=
            SystemConfig.studyEbbinghausPoint!.ebbinghausPoint!.length) {
          if (currentStudyRecord.studyLevel == 0) {
            currentStudyRecord.firstStudyDate = now;
          }

          StudyRecord studyRecord = StudyRecord.fromCurStudyRecord(currentStudyRecord);

          // 如果是升级，先计算时间，再升级。
          // 如果是降级，先降级，再计算时间。
          // 如果是平级，用降级的level计算时间，但level要置为平级
          if (currentStudyRecord.curAdjStudyLevel! > 0) {
            // 升级，先计算时间，再增加studyLevel
            studyRecord.calStudyTime(now, SystemConfig.repeatEbbinghausPoint!);
            studyRecord.studyLevel = studyRecord.studyLevel! + currentStudyRecord.curAdjStudyLevel!;
          } else if (currentStudyRecord.curAdjStudyLevel! < 0) {
            // 降级，先降级，再用低一级的ebbPoint计算
            studyRecord.studyLevel = studyRecord.studyLevel! + currentStudyRecord.curAdjStudyLevel!;
            studyRecord.calStudyTime_full(
                now, studyRecord.studyLevel! - 1 < 0 ? 0 : studyRecord.studyLevel! - 1, SystemConfig.repeatEbbinghausPoint!);
          } else {
            // 平级，用低一级的ebbPoint计算
            studyRecord.calStudyTime_full(
                now, studyRecord.studyLevel! - 1 < 0 ? 0 : studyRecord.studyLevel! - 1, SystemConfig.repeatEbbinghausPoint!);
          }

          currentStudyRecord.firstJudge = false;

          // TODO：应该用事务处理
          // 保存记录，有可能记录已删除，更新失败，忽略更新失败。
          var queryRes = await StudyRecordManager.shared.queryById(currentStudyRecord.id);
          if (queryRes != null) {
            var val = await StudyRecordManager.shared.update(studyRecord);
          }

          // 删除curStudyRecord
          var val = await CurStudyRecordManager.shared.deleteById(currentStudyRecord.id);
          return null;
        } else {
          // 未达到当天标准
          currentStudyRecord.calStudyTime(DateTime.now(), SystemConfig.studyEbbinghausPoint!); // 先计算，后level加1
          currentStudyRecord.curStudyLevel = currentStudyRecord.curStudyLevel! + 1;

          currentStudyRecord.firstJudge = false;

          // 更新时，查看是否已删除了对应的studyRecord，如果已删除，则删除当前curStudyRecord，没删的话，则更新curStudyRecord
          if (await StudyRecordManager.shared.queryById(currentStudyRecord.id) != null) {
            await CurStudyRecordManager.shared.update(currentStudyRecord);
          } else {
            await CurStudyRecordManager.shared.deleteById(currentStudyRecord.id);
          }
        }
      }
    }
  }

  Future<void> processForgetWord(CurStudyRecord currentStudyRecord) async {
    DateTime now = DateTime.now();

    // 如果当前单词已不在curStudyRecord中，则重新插入，如还在curStudyRecord中，则什么都不做
    await regetNoUseWordToCurRecord(currentStudyRecord);

    // 双0初级态的forget和blurry点击，不修改firstJudge
    if (currentStudyRecord.studyLevel != 0 || currentStudyRecord.curStudyLevel != 0) {
      if (currentStudyRecord.firstJudge == true) {
        // 如果是当天首次过这个单词，并且忘记了，则降级

        // 统计LevelSum
        // 超时过久的记录，不统计
        if (!currentStudyRecord.isTooLateToStatic(now)) {
          await statisticLevelSum(currentStudyRecord, currentStudyRecord.oriStudyLevel! - 1, now, 0, 1, 0);
        }

        // 0、1级，都不用降了，1级再降回0级，相当于当前这次白学
        currentStudyRecord.curAdjStudyLevel = currentStudyRecord.studyLevel! > 1 ? -1 : 0;

        if (currentStudyRecord.studyLevel! > 0) {
          // 0级的忘记，不改变neverSeen状态
          currentStudyRecord.neverSeen = false;// 为了统计数据准确，如果有过忘记，这个词就不作为neverSeen处理
        }
      }
    }

    // 更新CurStudyRecord
    // 当前级别降级，如果是满级，并且是第一轮判断（firstJudge为true）则降为满级减1级，否则只降1级
    int tStudyEbbLength = SystemConfig.studyEbbinghausPoint!.ebbinghausPoint!.length;
    if (currentStudyRecord.curStudyLevel! >= tStudyEbbLength &&
        currentStudyRecord.firstJudge == true) {
      currentStudyRecord.curStudyLevel = tStudyEbbLength - 1 > 0 ? tStudyEbbLength - 1 : 0;
    } else {
      currentStudyRecord.curStudyLevel =
      currentStudyRecord.curStudyLevel! > 1
          ? currentStudyRecord.curStudyLevel! - 1
          : 0;
    }

    // 计算下次学习时间
    currentStudyRecord.calStudyTime_full(
      DateTime.now(),
      currentStudyRecord.curStudyLevel! - 1,
      SystemConfig.studyEbbinghausPoint!,
    ); // 先降级，再计算时间

    //新词永远保持firstJudge为true，否则会导致选按了忘记，再按记得后，adj level 为 0
    if ((currentStudyRecord.curStudyLevel == 0) && (currentStudyRecord.studyLevel == 0))
      currentStudyRecord.firstJudge = true;
    else
      currentStudyRecord.firstJudge = false;

    // 更新时，查看是否已删除了对应的studyRecord
    // 如果已删除，则删除当前curStudyRecord
    // 没删的话，则更新curStudyRecord
    if (await StudyRecordManager.shared.queryById(currentStudyRecord.id) != null) {
      await CurStudyRecordManager.shared.update(currentStudyRecord);
    } else {
      await CurStudyRecordManager.shared.deleteById(currentStudyRecord.id);
    }
  }

  Future<void> processBlurryWord(CurStudyRecord currentStudyRecord) async {
    DateTime now = DateTime.now();

    // 如果当前单词已不在curStudyRecord中，则重新插入，如还在curStudyRecord中，则什么都不做
    await regetNoUseWordToCurRecord(currentStudyRecord);

    // 更新CurStudyRecord，不是StudyLevel

    // 双0初级态的forget和blurry点击，不修改firstJudge
    if (currentStudyRecord.studyLevel != 0 || currentStudyRecord.curStudyLevel != 0) {
      if (currentStudyRecord.firstJudge == true) {// 如果是当天首次过这个单词，并且模糊，则级别不变

        // 超时过久的记录，不统计
        if (!currentStudyRecord.isTooLateToStatic(now)) {
          // 统计LevelSum
          await statisticLevelSum(currentStudyRecord, currentStudyRecord.oriStudyLevel! - 1, now, 0, 0, 1 );
        }

        // 模糊的仍然升级，只是今天要多次重复记忆
        if (currentStudyRecord.studyLevel! >= SystemConfig.repeatEbbinghausPoint!.ebbinghausPoint!.length) {
          currentStudyRecord.curAdjStudyLevel = SystemConst.TOP_REPEAT_LEV - currentStudyRecord.studyLevel!;
        } else {
          currentStudyRecord.curAdjStudyLevel = 1;
        }

        // 有过模糊的词，neverSeen不改变，因为模糊状态，记忆级别仍然会升。
        // 所以注释掉这步操作。
        // if (currentStudyRecord.studyLevel > 0) //0级的忘记，不改变neverSeen状态
        //     currentStudyRecord.neverSeen = false; //为了统计数据准确，如果有过模糊、或忘记，这个词就不作为neverSeen处理
      }
    }

    // 更新CurStudyRecord，不是StudyLevel
    // 当前级别降级，如果是满级，并且是首次次判断，则降1级，否则不降级
    int tStudyEbbLength = SystemConfig.studyEbbinghausPoint!.ebbinghausPoint!.length;
    if (currentStudyRecord.curStudyLevel! >= tStudyEbbLength &&
        currentStudyRecord.firstJudge == true) {
      currentStudyRecord.curStudyLevel = tStudyEbbLength - 1 > 0 ? tStudyEbbLength - 1 : 0;
    }


    // 计算下次学习时间
    currentStudyRecord.calStudyTime_full(DateTime.now(), currentStudyRecord.curStudyLevel! - 1, SystemConfig.studyEbbinghausPoint!);

    //新词永远保持firstJudge为true，否则会导致选按了忘记，再按记得后，adj level 为 0
    if ((currentStudyRecord.curStudyLevel == 0) && (currentStudyRecord.studyLevel == 0))
      currentStudyRecord.firstJudge = true;
    else
      currentStudyRecord.firstJudge = false;

    // 更新时，查看是否已删除了对应的studyRecord
    // 如果已删除，则删除当前curStudyRecord
    // 没删的话，则更新curStudyRecord
    if (await StudyRecordManager.shared.queryById(currentStudyRecord.id) != null) {
      await CurStudyRecordManager.shared.update(currentStudyRecord);
    } else {
      CurStudyRecordManager.shared.deleteById(currentStudyRecord.id);
    }
  }

  Future<void> statisticLevelSum(CurStudyRecord curStudyRecord, int statStudyLev, DateTime time, int remberInt, int forgetInt, int blurryInt) async {
    if (statStudyLev < 0) {
      // studyLevel等于0时不用记
      return;
    }

    // 以用户id、studyLevel、ebbinghaus String作为条件，查检有无记录
    List<StatisticsLevelSum>? sumListInDb = await StatisticsLevelSumManager.shared.queryByUseridStudyLvEbbstr(
        curStudyRecord, SystemConfig.user!.id, statStudyLev, time);

    // 无记录则新增一条
    if (CollectionUtil.isEmpty(sumListInDb)) {
      StatisticsLevelSum newSum = StatisticsLevelSum.fromParam(
        id: UuidUtil.getUuid(),
        userId: SystemConfig.user!.id,
        studyLevel: statStudyLev,
        durationStr: StringUtil.isEmpty(curStudyRecord.curEbbinghausStr)
            ? SystemConfig.repeatEbbinghausPoint!.pointStrList![statStudyLev]
            : curStudyRecord.curEbbinghausStr!,
        lastUpdateTime: time,
        startStatTime: time,
        remberSum: remberInt,
        forgetSum: forgetInt,
        blurrySum: blurryInt,
        pointChgTime: DateTime.fromMillisecondsSinceEpoch(0),
        neverSeen: curStudyRecord.neverSeen,
      );

      StatisticsLevelSumManager.shared.addNewStatisticsLevelSum(newSum);
      return;
    }

    // 有记录则增加对应的统计值，然后更新
    StatisticsLevelSum sumInDb = sumListInDb![0];
    sumInDb.remberSum = sumInDb.remberSum! + remberInt;
    sumInDb.forgetSum = sumInDb.forgetSum! + forgetInt;
    sumInDb.blurrySum = sumInDb.blurrySum! + blurryInt;
    sumInDb.lastUpdateTime = time;
    sumInDb.neverSeen = curStudyRecord.neverSeen;
    StatisticsLevelSumManager.shared.updateStatisticsLevelSumById(sumInDb);

    return;
  }

  Future<void> regetNoUseWordToCurRecord(CurStudyRecord currentStudyRecord) async {
    // 如果词在curRecord中，则返回
    CurStudyRecord? curStudyRecord =
    await CurStudyRecordManager.shared.queryCurStudyRecordById(currentStudyRecord.id);
    if (curStudyRecord != null) {
      return;
    }

    // 如果不在，则从设置级别为最高，然后返回。因为每次取到新词插入currentStudyRecord时，curStudyLevel设为最高
    // 并置firstJudge标志
    currentStudyRecord.curStudyLevel =
        SystemConfig.studyEbbinghausPoint!.ebbinghausPoint!.length;
    currentStudyRecord.firstJudge = true;

    addCurStudyRecord(currentStudyRecord);
    return;
  }

  addCurStudyRecord(CurStudyRecord currentStudyRecord) {
    return CurStudyRecordManager.shared.insert(currentStudyRecord);
  }

  Future<StudyRecord?> queryStudyRecordByContendId(String? contendId) async {
    return await StudyRecordManager.shared.queryByContendId(contendId);
  }

  Future<CurStudyRecord?> queryCurStudyRcdByContendId(String? contendId) async {
    CurStudyRecord? queryRes = await CurStudyRecordManager.shared.queryByContendId(contendId);
    if (queryRes == null)
      return null;
    queryRes.initNoUse();
    return queryRes;
  }

  Future<int> addAllCurStudyRecord() async {
    List<StudyRecord>? recordList;
    int pageSize = 512;
    int currentStart = 0;
    int curPageSize = 0;
    int actruallyAdded = 0;

    DateTime time = DateTime.now();

    int startMillis = DateTime.now().millisecondsSinceEpoch;
    do{
      int loopStep1Millis = DateTime.now().millisecondsSinceEpoch;

      recordList = await calStudyRecordByPage(SystemConfig.user!.id, time, currentStart, pageSize);
      curPageSize = CollectionUtil.size(recordList);

      int loopStep2Millis = DateTime.now().millisecondsSinceEpoch;

      actruallyAdded = actruallyAdded + await refreshCurStudyRecore(recordList); //当前学习记录中，无则插入，有则忽略（不修改）

      int loopStep3Millis = DateTime.now().millisecondsSinceEpoch;

      currentStart += curPageSize;
    } while (curPageSize >= pageSize);

    int stopMillis = DateTime.now().millisecondsSinceEpoch;

    return actruallyAdded;
  }

  Future<List<StudyRecord>?> calStudyRecordByPage(String userId, DateTime time, int currentStart, int pageSize) async {
    return await StudyRecordManager.shared.calStudyRecordByPage(userId, time, currentStart, pageSize);
  }

  Future<int> refreshCurStudyRecore(List<StudyRecord>? recordList) async {
    if (CollectionUtil.isEmpty(recordList))
      return 0;
    int res = 0;
    List<String>? idList;
    idList = StudyRecord.buildIdListFromList(recordList);
    List<CurStudyRecord>? curRecordListInDb = await CurStudyRecordManager.shared.queryCurStudyRecordByIdList(idList);

    Map<String, CurStudyRecord>? curRecordDict;
    curRecordDict = CurStudyRecord.buildIdIndexedMap(curRecordListInDb);

    List<CurStudyRecord> insertList = [];
    for (StudyRecord curRecord in recordList!) {
      if (curRecordDict == null || (curRecordDict[curRecord.id] == null)) {
        CurStudyRecord  curStudyRecord = CurStudyRecord.fromRecord(curRecord);
        insertList.add(curStudyRecord);
        res ++;
      }
    }
    CurStudyRecordManager.shared.insertList(insertList);
    return res;
  }

  Future<List<WordDto>?> transferBookContentToWordDto(List<BookContent>? contents) async {
    if (CollectionUtil.isEmpty(contents))
      return null;
    List<String> contentIds = [];
    for (BookContent content in contents!) {
      contentIds.add(content.contentSearchId);
    }

    List<Word>? words =  await WordManager.shared.queryWordBySearchIdList(contentIds);

    List<WordDto>? res = await convertWordContentToWordDtoList(contents, words);

    return arrangeSequence(contents, res);
  }

  //以contents为准，根据wordList的数据，生成WordDto列表。contents中有，wordList没有的，也生成一条记录。为了提示
  //课本中的词在词典中不存在，同时也方便列表位置的计算
  List<WordDto>? convertWordContentToWordDtoList(List<BookContent>? contents, List<Word>? words) {
    if (contents == null)
      return null;

    List<Word> tmpWords = words == null?[]:words;

    List<WordDto> res = [];

    Map<String, Word> wordMap = {};
    for (Word word in tmpWords) {
      wordMap[word.searchId!] = word;
    }

    for (BookContent content in contents){
      Word? word = wordMap[content.contentSearchId];
      if (word != null) {
        WordDto tmpDto = new WordDto.fromWord(word, false, false);
        res.add(tmpDto);
      } else {
        Word tmpWord = new Word.fromParams(content.contentSearchId, null);
        WordDto tmpDto = new WordDto.fromWord(tmpWord, false, false);
        tmpDto.setIsInDictState(false);
        res.add(tmpDto);
      }
    }
    return res;
  }


  Future<List<WordDto>?> arrangeSequence(List<BookContent>? contents, List<WordDto>? dataList) async {
    if (CollectionUtil.isEmpty(contents) ||  CollectionUtil.isEmpty(dataList))
      return null;

    Map<String, WordDto> dataMap = {};
    for (WordDto data in dataList!) {
      dataMap[data.word!.searchId!] = data;
    }

    List<WordDto> res = [];

    for (BookContent content in contents!) {
      WordDto? tData = dataMap[content.contentSearchId];
      if (tData != null) {
        tData.sequence = content.sequence;
        res.add(tData);
      }
    }
    return res;
  }

  Future<List<WordDto>?> buildWordListRecordStat(List<WordDto>? dtoList) async {
    {
      if (CollectionUtil.isEmpty(dtoList))
        return null;
      List<String>? idList = buildIdListFromDtoList(dtoList);

      List<StudyRecord>? recordList = await StudyRecordManager.shared.queryByContendIdList(idList);

      for (WordDto dto in dtoList!)
        dto.setAddedState(false);

      if (CollectionUtil.isEmpty(recordList)) {
        return dtoList;
      }

      Map<String, StudyRecord> recordMap = {};
      for (StudyRecord record in recordList!) {
        recordMap[record.contentId!] = record;
      }

      for (WordDto wordDto in dtoList) {
        StudyRecord? tStudyRcd = recordMap[wordDto.word!.id];
        if (tStudyRcd != null) {
          wordDto.setAddedState(true);
        }
      }

      return dtoList;
    }
  }

  List<String>? buildIdListFromDtoList(List<WordDto>? dtoRes) {
    if (CollectionUtil.isEmpty(dtoRes))
      return null;
    List<String> res = [];
    for (WordDto wordDto in dtoRes!) {
      if (wordDto.word!.id != null)
        res.add(wordDto.word!.id!);
    }
    return res;
  }

  searchWordContentByPatternByPage(String? serchIdPattern, String? meaningPattern, Word? relateSource, int? begin, int? actPageSize) async {
    List<Word>? searchRes = await WordManager.shared.searchWordByPatternByPage(serchIdPattern, meaningPattern, begin, actPageSize);

    if (CollectionUtil.isEmpty(searchRes))
      return null;

    List<WordDto>? dtoRes = convertWordToWordDtoList(searchRes);
    await buildWordListRecordStat(dtoRes);
    if (relateSource != null) {
      buildWordListRecordRelationStat(dtoRes, relateSource);
    }
    return dtoRes;
  }

  List<WordDto>? convertWordToWordDtoList(List<Word>? wordList) {
    if (wordList == null)
      return null;
    List<WordDto> res = [];
    for (Word word in wordList) {
      res.add(WordDto.fromWord(word, false, false));
    }
    return res;
  }

  Future<bool> wordStudyRecordExisted(String? wordId) async {
    bool res = await StudyRecordManager.shared.wordStudyRecordExisted(wordId);
    return res;
  }

  Future <int> addStrangeWordToRecord(Word? word) async {
    return await StudyRecordManager.shared.addStrangeWordToRecord(word);
  }

  bool isWordStudyStarted(StudyRecord studyRecord, CurStudyRecord? curStudyRecord) {
    if (studyRecord.studyLevel != null && studyRecord.studyLevel! > 0)
      return true;

    if (curStudyRecord == null)
      return false;
    if (curStudyRecord.curStudyLevel != null && curStudyRecord.curStudyLevel! > 0)
      return true;

    return false;

  }

  deleteBothStudyRecord(StudyRecord studyRecord, CurStudyRecord? curStudyRecord) async {
      //删除学习记录
      await deleteStudyRecord(studyRecord);
      deleteCurStudyRecord(curStudyRecord);
      return true;
  }

  Future<int> deleteStudyRecord(StudyRecord studyRecord) async {
    return await StudyRecordManager.shared.deleteStudyRecord(studyRecord);
  }

  Future<int> addMultipleWordRelation(List<WordRelation> relationList) async {
    return await WordRelationManager.shared.insertMultiple(relationList);
  }

  Future<int> addWordRelation(WordRelation relation) async {
    return await WordRelationManager.shared.insert(relation);
  }

  Future<int> deleteWordRelationList(List<WordRelation>? relations) async {
    return await WordRelationManager.shared.deleteWordRelationList(relations);
  }

  Future<int> deleteWordRelation(WordRelation relation) async {
    return await WordRelationManager.shared.deleteWordRelation(relation);
  }

  Future<List<WordDto>?> buildWordListRecordRelationStat(List<WordDto>? dtoRes, Word curWord)  async {
    if (CollectionUtil.isEmpty(dtoRes))
      return null;

    for (WordDto dto in dtoRes!)
      dto.setRelatedState(false);
    List<WordRelation>? allRelations = await queryAllRelationForWord(curWord);
    if (CollectionUtil.isEmpty(allRelations)) {
      return dtoRes;
    }

    //置状态
    Map<String,WordRelation> relationMap = {};
    for (WordRelation relation in allRelations!) {
      String? efficientId = relation.getEfficientWordId(curWord.id);
      if (StringUtil.nonEmpty(efficientId))
        relationMap[efficientId!] = relation;
    }

    for (WordDto wordDto in dtoRes) {
      WordRelation? tWordRelation = relationMap[wordDto.word!.id!];
      if (tWordRelation != null) {
        wordDto.setRelatedState(true);
      }
    }
    return dtoRes;
  }

  Future<List<WordRelation>?> queryAllRelationForWord(Word curWord) async {
    return await WordRelationManager.shared.queryAllDirectRelationForWord(curWord);
  }

  updateInsertWord(Word newWord) async {
    return await WordManager.shared.updateInsertWord(newWord);
  }

  void copyCommShowValue(CurStudyRecord? srcStudyRecord, CurStudyRecord? tgtStudyRecord) {
    if ((srcStudyRecord != null) && (tgtStudyRecord != null)){
      tgtStudyRecord.studyLevel = srcStudyRecord.studyLevel;
      tgtStudyRecord.curStudyLevel = srcStudyRecord.curStudyLevel;
      tgtStudyRecord.noUse = srcStudyRecord.noUse;
      tgtStudyRecord.firstJudge = srcStudyRecord.firstJudge;
      tgtStudyRecord.curEbbinghausStr = srcStudyRecord.curEbbinghausStr;
    }
  }

  Future<bool> shareBackupDb(BuildContext context) async {
    bool result = false;
    await DBManager.shared.closeDb();
    ShareResult shareResult = await Share.shareXFiles([XFile(SystemConfig.FULL_DB_PATH)], text: 'Backup Database');
    if (shareResult.status != ShareResultStatus.success) {
      ToastUtil.showToast(LocStr.of(context)!.restoreFailPlsRetry);
      result = false;
    } else {
      result = true;
    }
    await DBManager.shared.openDb(SystemConfig.FULL_DB_PATH);
    return result;
  }

  //0: 已有文件，不用下载。 1：下载成功。-1：url未设置，-2：下载错误
  Future<int> downloadVoiceFile(String wordSearchId) async {
    if (MediaUtils.soundFileExists(wordSearchId))
      return 0;
    if (StringUtil.isEmpty(SystemConfig.getUserProperty(SystemConst.P_VOICE_DOWNLOAD_URL)))
      return -1;
    var url = SystemConfig.getUserProperty(SystemConst.P_VOICE_DOWNLOAD_URL)!.replaceAll("\${word}",wordSearchId);

    try {
      var response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        var bytes = response.bodyBytes;
        var resultFilePath = SystemConfig.SOUND_BASE_PATH + Platform.pathSeparator +
            MediaUtils.buildMediaFilePath(wordSearchId);
        var dirPath = SystemConfig.SOUND_BASE_PATH + Platform.pathSeparator +
            MediaUtils.buildMediaRelativeDirPath(wordSearchId);
        //自动创建目录
        var dir = Directory(dirPath);
        if (!dir.existsSync()) {
          dir.createSync(recursive: true); // 使用 recursive: true 可以创建所有必要的父目录
        }

        var savedFile = File(resultFilePath);
        await savedFile.writeAsBytes(bytes);

        return 1;
      } else {
        return -2;
      }
    } catch (err) {
      // ToastUtil.showToast('声音下载失败');
      return -2;
    }
  }

  deleteAllRecordOfWord(Word word) async {
    //TODO 应该用事务处理
    await deleteStudyRecordByContentId(word.id!);
    await deleteCurStudyRecordByContentId(word.id!);
    return 1;
  }

  deleteCurStudyRecordByContentId(String contentId) async {
    return await CurStudyRecordManager.shared.deleteCurStudyRecordByContendId(contentId);
  }

  deleteWordSound(String? wordSearchId) async {
    if (StringUtil.isEmpty(wordSearchId))
        return 0;
    if (!MediaUtils.soundFileExists(wordSearchId!))
      return 2;
    String filePath = SystemConfig.SOUND_BASE_PATH + Platform.pathSeparator +
        MediaUtils.buildMediaFilePath(wordSearchId);
    var soundFile = File(filePath);
    await soundFile.delete();
    return 1;
  }

  Future<List<StatisticsCountDto>?> queryCommingCountByPage(String userId, int? begin, int? pageSize) async {
    return await StudyRecordManager.shared.queryCommingCountByPage(userId, begin, pageSize);
  }

}