
import 'package:words/biz/word_biz.dart';
import 'package:words/biz/book_biz.dart';
import 'package:words/common/recyclerList/list_query_callable.dart';
import 'package:words/domain/book_content.dart';
import 'package:words/dto/word_dto.dart';

class BookContentQuerier extends ListQueryCallable {

  String? bookId;
  Object? param;
  bool hideStudied = false;

  BookContentQuerier(String bookId, bool hideStudied) {
    this.hideStudied = hideStudied;
    this.bookId = bookId;
  }

  Future<List<BookContent>?> queryByPage(int? begin, int? pageSize) async {
    List<BookContent>? searchContents = await BookBiz.shared.searchBookContentByPage(bookId, begin, pageSize);
    return searchContents;
  }

  Future<List<BookContent>?> filterContent(List? sourceList) async {
    List<BookContent>? filterRes = await BookBiz.shared.filterBookContentByStudied(sourceList as List<BookContent>, this.hideStudied);
    return filterRes;
  }

  Future<List<WordDto>?> inflateData(List? sourceList) async {
    List<WordDto>? dtoList = await WordBiz.shared.transferBookContentToWordDto(sourceList!.cast<BookContent>().toList());
    List<WordDto>? wordDtos = await WordBiz.shared.buildWordListRecordStat(dtoList);
    return wordDtos;
  }

  void setParam(Object param) {
    this.param = param;
    return;
  }

}
