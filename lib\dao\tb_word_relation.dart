import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/word.dart';
import 'package:words/domain/word_relation.dart';
import 'package:words/utils/other/UserInfo.dart';

class TbWordRelation extends DBBaseProvider {

  @override
  String get tableName => "WORD_RELATION";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID text not null,
        RELATION_ID text,
        WORD_ID text,
        OWNER_ID text
        )
        ''';
  }


  addRelation(String sourceId, Word entity) async {
    WordRelation wordRelationEntity = WordRelation();
    wordRelationEntity.id = Uuid().v4().toLowerCase().replaceAll("-", "");
    wordRelationEntity.relationId = sourceId;
    wordRelationEntity.wordId = entity.id!;
    wordRelationEntity.ownerId = UserInfo.kTestUserId;
    return await baseInsert(wordRelationEntity.toJson());
  }

  removeRelation(String relationId, String wordId) async {
    final Database? db = await dataBase;
    return await db?.delete(tableName, where: "RELATION_ID = ? and WORD_ID = ?", whereArgs: [relationId, wordId]);
  }

}