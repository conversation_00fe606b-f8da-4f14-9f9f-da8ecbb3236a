import 'package:flutter/material.dart';
import 'package:words/l10n/loc_str.dart';

class HomeFourView extends StatefulWidget {
  final Function clickHandler;
  HomeFourView(this.clickHandler);
  _HomeFourViewState createState() => _HomeFourViewState();
}

class _HomeFourViewState extends State<HomeFourView> {
  Color _color0 = Colors.green;
  Color _color1 = Colors.blue;
  Color _color2 = Colors.red[400]!;

  @override
  Widget build(BuildContext context) {
    return content();
  }

  final double _boxS = 110;

  content(){
    return Container(
      height: 110,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          SizedBox(width: 5,),
          GestureDetector(
            onTapDown: (details) {
              setState(() {
                _color0 = Colors.grey;
              });
            },
            onTapUp: (details) {
              setState(() {
                _color0 = Colors.green;
              });
            },
            onTapCancel: () {
              setState(() {
                _color0 = Colors.green;
              });
            },
            child: Container(
              width: _boxS,
              height: _boxS,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: _color0,
              ),
              child: Center(
                child: Text(
                  LocStr.of(context)!.remember!,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 18
                  ),
                ),
              ),
            ),
            onTap: (){
              widget.clickHandler(0);
              setState(() {
                _color0 = Colors.green;
              });
            },
          ),
          GestureDetector(
            onTapDown: (details) {
              setState(() {
                _color1 = Colors.grey;
              });
            },
            onTapUp: (details) {
              setState(() {
                _color1 = Colors.blue;
              });
            },
            onTapCancel: () {
              setState(() {
                _color1 = Colors.blue;
              });
            },
            child: Container(
              width: _boxS,
              height: _boxS,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: _color1,
              ),
              child: Center(
                child: Text(
                  LocStr.of(context)!.uncertain!,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 18
                  ),
                ),
              ),
            ),
            onTap: (){
              widget.clickHandler(1);
            },
          ),
          GestureDetector(
            onTapDown: (details) {
              setState(() {
                _color2 = Colors.grey;
              });
            },
            onTapUp: (details) {
              setState(() {
                _color2 = Colors.red[400]!;
              });
            },
            onTapCancel: () {
              setState(() {
                _color2 = Colors.red[400]!;
              });
            },
            child: Container(
              width: _boxS,
              height: _boxS,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: _color2,
              ),
              child: Center(
                child: Text(
                  LocStr.of(context)!.forgot!,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 18
                  ),
                ),
              ),
            ),
            onTap: (){
              widget.clickHandler(2);
            },
          ),
          SizedBox(width: 5,),
        ],
      ),
    );
  }
}