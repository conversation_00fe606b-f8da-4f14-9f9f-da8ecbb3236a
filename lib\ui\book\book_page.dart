import 'package:flutter/material.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/book.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/utils/router/word_router.dart';

class BookPage extends StatefulWidget {
  _BookPageState createState() => _BookPageState();
}

class _BookPageState extends State<BookPage> with TickerProviderStateMixin {

  List<Book> _dataSource = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Center(
          child: Text(LocStr.of(context)!.book!),
        ),
      ),
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: FutureBuilder(
          future: _makeData(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              // return BookListView(snapshot.data as List<BookEntity>);
              final List<Book> _result = (snapshot.data as List<Book>?) ?? [];
              _dataSource = _result;
              return GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 24.0,
                    crossAxisSpacing: 24.0,
                    childAspectRatio: 1.0,
                ),
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 16, bottom: 16),
                physics: const BouncingScrollPhysics(),
                scrollDirection: Axis.vertical,
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8.0),
                          bottomLeft: Radius.circular(8.0),
                          bottomRight: Radius.circular(8.0),
                          topRight: Radius.circular(8.0)),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                            color: Color(0xFF3A5160).withOpacity(0.4),
                            offset: const Offset(1.1, 1.1),
                            blurRadius: 10.0),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        focusColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                        splashColor: Color(0xFF2633C5).withOpacity(0.2),
                        onTap: () {
                          Navigator.pushNamed(context, WordRouter.bookContent, arguments: {"book": _dataSource[index]});
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Text(_dataSource[index].name),
                            SizedBox(height: 15,),
                            Image.asset("assets/image/book_bg.png")
                          ],
                        ),
                      ),
                    ),
                  );
                },
                itemCount: _dataSource.length,
              );
            }else{
              return Center(
                child: Container(),
              );
            }
          },
        ),
      )
    );
  }

  Future<List<Book>> _makeData() async {
    List<Book> ls = await DBManager.shared.tbBook.queryList(_dataSource.length~/20);
    return ls;
  }
}
