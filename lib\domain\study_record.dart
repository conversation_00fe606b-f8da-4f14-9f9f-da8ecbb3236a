import 'dart:math';

import 'package:json_annotation/json_annotation.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/domain/ebbinghaus_point.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'dart:convert';

import 'package:words/common/system_const.dart';
import 'package:words/commonSupport/utils/type_serial_util.dart';
import 'package:words/domain/word.dart';
import 'cur_study_record.dart';

part 'study_record.g.dart';

@JsonSerializable()
class StudyRecord {
	@Json<PERSON>ey(name: "ID")
	String? id;

	@JsonKey(name: "CONTENT_SEARCH_ID")
	String? contentSearchId;

	@Json<PERSON>ey(name: "USER_ID")
	String? userId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "STUDY_LEVEL")
	int? studyLevel;

	@J<PERSON><PERSON><PERSON>(name: "FIRST_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? firstStudyDate;

	@JsonKey(name: "LAST_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? lastStudyDate;

	@JsonKey(name: "MIN_NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? minNextStudyDate;

	@JsonKey(name: "NEXT_STUDY_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? nextStudyDate;

	@JsonKey(name: "NEVER_SEEN", fromJson: TypeSerialUtil.dynamicToBool, toJson: TypeSerialUtil.boolToInt)
	bool? neverSeen;

	@JsonKey(name: "ADD_DATE", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? addDate;

	@JsonKey(name: "CUR_EBBINGHAUS_STR")
	String? curEbbinghausStr;

	@JsonKey(name: "CONTENT_ID")
	String? contentId;

	StudyRecord();
	StudyRecord.fromWord(Word word) {
		this.id = UuidUtil.getUuid();
		this.contentId = word.id;
		this.contentSearchId = word.searchId;
		this.userId = SystemConfig.user!.id;
	}
	factory StudyRecord.fromJson(Map<String, dynamic> json) => _$StudyRecordFromJson(json);

	Map<String, dynamic> toJson() => _$StudyRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}


	StudyRecord.fromCurStudyRecord(CurStudyRecord currentStudyRecord) {
		this.id = currentStudyRecord.id;
		this.contentSearchId = currentStudyRecord.contentSearchId;
		this.contentId = currentStudyRecord.contentId;
		this.userId = currentStudyRecord.userId;
		this.studyLevel = currentStudyRecord.studyLevel;
		this.firstStudyDate = currentStudyRecord.firstStudyDate;
		this.addDate = currentStudyRecord.addDate;
		this.lastStudyDate = currentStudyRecord.lastStudyDate;
		this.minNextStudyDate = currentStudyRecord.minNextStudyDate;
		this.nextStudyDate = currentStudyRecord.nextStudyDate;
		this.curEbbinghausStr = currentStudyRecord.curEbbinghausStr;
		this.neverSeen = currentStudyRecord.neverSeen;
	}

	void calStudyTime(DateTime date, EbbinghausPoint ebbinghausPoint) {
		calStudyTime_full(date, this.studyLevel!, ebbinghausPoint);
	}

	void calStudyTime_full(DateTime date, int calLevel, EbbinghausPoint ebbinghausPoint) {
		if (this.studyLevel == 0) {
			this.firstStudyDate = date;
		}
		this.lastStudyDate = date;

		Duration offset;
		if (calLevel == SystemConst.TOP_REPEAT_LEV) {
			offset = Duration(milliseconds: SystemConst.TOP_REPEAT_DURATION);
			this.curEbbinghausStr = SystemConst.TOP_REPEAT_DURATION_STR;
		} else if (calLevel >= ebbinghausPoint.ebbinghausPoint!.length) {
			offset = Duration(milliseconds: SystemConst.TOP_REPEAT_DURATION);
			this.curEbbinghausStr = SystemConst.TOP_REPEAT_DURATION_STR;
		} else {
			offset = Duration(milliseconds: ebbinghausPoint.ebbinghausPoint![calLevel < 0 ? 0 : calLevel]);
			this.curEbbinghausStr =
			ebbinghausPoint.pointStrList![calLevel < 0 ? 0 : calLevel];
		}

		this.nextStudyDate = date.add(offset)
				.add(Duration(milliseconds: (offset.inMilliseconds * (-0.03 + ((0.03  + 0.03) * Random().nextDouble()))).round())); //加上一个随机分散数，-0.03到+0.03

		this.minNextStudyDate =
				DateTime.fromMillisecondsSinceEpoch(DateUtil.calMinPointOffset(date.millisecondsSinceEpoch, this.nextStudyDate!.millisecondsSinceEpoch));
	}

  static List<String>? buildIdListFromList(List<StudyRecord>? recordList) {
		if (CollectionUtil.isEmpty(recordList))
			return null;
		List<String> result = [];
		for (StudyRecord rcd in recordList!){
			result.add(rcd.id!);
		}
		return result;
	}

  static Map<String, StudyRecord>? buildContentSearchIdIndexedMapFromList(List<StudyRecord>? studyRecords) {
		if (CollectionUtil.isEmpty(studyRecords))
			return null;
		Map<String, StudyRecord> result = {};
		for (StudyRecord rcd in studyRecords!) {
			result[rcd.contentSearchId!] = rcd;
		}
		return result;
	}

}