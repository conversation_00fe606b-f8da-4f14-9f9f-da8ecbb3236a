import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/domain/word.dart';
import 'package:words/common/system_config.dart';
import 'package:words/domain/click_counter.dart';
import 'package:words/domain/cur_study_rcd_buffer.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/history_cur_study_rcd_buffer.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(HomeState()) {
    init();
  }

  Color color0 = Colors.green;
  Color color1 = Colors.blue;
  Color color2 = Colors.red[400]!;

  final ValueNotifier<int> bottomOperationViewNotifier = ValueNotifier<int>(0);
  WordDto? viewWord = WordDto();
  CurStudyRecord? viewCurStudyRecord;
  int? remainExpired = 0;
  int? remainExpiredStarted = 0;
  int? remainNew = 0;
  int? remainNewStarted = 0;
  int? remainToday = 0;
  int? remainTodayStarted = 0;
  int? remainComming = 0;
  int? remainCommingStarted = 0;

  String? nextTimeOfAll = "";

  String? wordNextStudyTime = "";
  String? wordLastStudyTime = "";

  int showRelationStep = 0;
  bool? meaningVisible;
  bool silentMode = true;

  CurStudyRcdBuffer curStudyRcdBuffer = CurStudyRcdBuffer();
  HistoryCurStudyRcdBuffer historyCurStudyRcdBuffer = HistoryCurStudyRcdBuffer(256);

  Word? currentWord;
  List<WordDto>? curRelatedWords;

  CurStudyRecord? currentStudyRecord;
  int curShowStep = 0;

  bool forceEdit = false;
  ClickCounter linkBtnClickCounter = ClickCounter(3);

  Future<void> init() async {
    viewWord = WordDto.simple(Word.fromParams("", ""));
    viewCurStudyRecord = CurStudyRecord();

    showRelationStep = 0;
    if (!isClosed)
      emit(HomeState()..showRelationStep = this.showRelationStep!);

    await refreshBuffer();
  }


  studyNextWord(BuildContext context) async{
    var now = DateTime.now();

    var curRelateWordPos = 0;
    var curRelateWordPosOffset = 0;

    calRemainCurRecord(); // Because remain data is used to fill words, calculate it first

    calNextEffectStartTime(SystemConfig.user!.id, now); // Calculate the next effective time for display at the top

    Map<String, dynamic>? queryRes = await getCurStudyWord(SystemConfig.user!.id, now);

    if (CollectionUtil.isEmpty(queryRes)) {
      currentWord = null;
      currentStudyRecord = null;
      ToastUtil.showToast(LocStr.of(context)!.noWordBeingStudied);
    } else {
      currentWord = queryRes?['word'];
      currentStudyRecord = queryRes?['curStudyRecord'];
      if (currentWord == null && currentStudyRecord != null) {
        WordBiz.shared.deleteCurStudyRecord(currentStudyRecord);
        WordBiz.shared.deleteStudyRecordByContentId(currentStudyRecord!.contentId!);
        currentStudyRecord = null;
      } else {
        playWordSound(currentWord);
      }
    }

    calCurNextTimeStr(); // Calculate the display time string

    await searchRelatedWord();

    zeroStepShowWord();
  }

  calRemainCurRecord() async{
    var now = DateTime.now();

    //剩余的到期需学习的词数
    this.remainExpired = await WordBiz.shared.calRemainExpiredCurStudyRcd(now);
    this.remainExpiredStarted = await WordBiz.shared.calRemainExpiredStartedCurStudyRcd(now);

    //剩余新词数，包括开始学习，但未完成的
    this.remainNew = await WordBiz.shared.calRemainNewCurStudyRcd(now);

    //已开始学习、未完成的新词数
    this.remainNewStarted = await WordBiz.shared.calRemainNewStartedCurStudyRcd(now);

    //未到期，但今天4点前会到期的词数
    this.remainToday = await WordBiz.shared.calRemainTodayCurStudyRcd(now);
    this.remainTodayStarted = await WordBiz.shared.calRemainTodayStartedCurStudyRcd(now);

    //未到期，但可以提前的词数
    this.remainComming = await WordBiz.shared.calRemainCommingCurStudyRcd(now);

    //未到期，可以提前，已开始学习的词数
    this.remainCommingStarted = await WordBiz.shared.calRemainCommingStartedCurStudyRcd(now);

  }

  Future<void> calNextEffectStartTime(String userId, DateTime now) async {
    this.nextTimeOfAll = await WordBiz.shared.calNextEffectStartTime(userId, now);
  }

  Future<Map<String, Object>?> getCurStudyWord(String userId, DateTime now) async {
    CurStudyRecord? result = await WordBiz.shared.getNextFromCurStyRcdInSequence(userId, now);

    // 如果没有，在buffer所存的所有词表中随机取一个，要置nouse标志，用于点击按钮时不修改数据
    if (result == null) {
      // 如果已全背完，直接返回
      if ((remainExpired == null || remainExpired == 0) &&
          (remainNew == null || remainNew == 0) &&
          (remainComming == null || remainComming == 0)) {
        return null;
      }
      CurStudyRecord? randomRecord = curStudyRcdBuffer.getCurStudyRecordFromBuffer();

      if (randomRecord == null) {
        return null;
      }

      randomRecord.noUse = -1;

      result = randomRecord;
    }

    Map<String, Object> res = {};
    res['curStudyRecord'] = result;

    Word? curWord = await WordBiz.shared.queryWordByIdOfUserNSys(result.contentId!, SystemConfig.user!.id);
    if (curWord != null)
      res['word'] = curWord;
    return res;
  }

  void playWordSound(Word? word) {
    if (word == null)
      return;
    if (silentMode == false)
      WordBiz.shared.playWordSound(word!.searchId);
  }

  void calCurNextTimeStr() {
    if (this.currentStudyRecord != null) {
      wordNextStudyTime = DateUtil.date2String(this.currentStudyRecord!.nextStudyDate, "yy.MM.dd HH:mm:ss");
      wordLastStudyTime = DateUtil.date2String(this.currentStudyRecord!.lastStudyDate, "yy.MM.dd HH:mm");
    } else {
      wordNextStudyTime = "";
      wordLastStudyTime = "";
    }
  }

  Future<void> searchRelatedWord() async {
    this.curRelatedWords = await WordBiz.shared.searchRelatedWord(this.currentWord, this.showRelationStep!);
  }

  void zeroStepShowWord() {
    this.curShowStep = 0;
    if (currentWord == null) {
      nullShowSetValue();
    }else {
      //if (MediaUtils.soundFileExists(currentWord!.searchId!)) {
      if (silentMode == false) { //因为使用的tts，如果语音文件不存在，也会用tts发音，所以这里不需要判断了
        viewWord!.word!.word = "";
        meaningVisible = false;
        viewWord!.word!.meaning = "";
        viewWord!.editable = WordDto.calEditableTool(this.currentWord, this.forceEdit);

        viewCurStudyRecord!.studyLevel = null;
        viewCurStudyRecord!.curStudyLevel = null;
        viewCurStudyRecord!.curEbbinghausStr = null;
        viewCurStudyRecord!.firstJudge = true;
        if (currentStudyRecord != null) {
          viewCurStudyRecord!.noUse = currentStudyRecord!.noUse;
        } else {
          viewCurStudyRecord!.noUse = 1;
        }

        WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
      } else {
        firstStepShowWord();
      }
    }
    if (!isClosed)
      emit(HomeState.showWord(word: viewWord!.word, history: historyCurStudyRcdBuffer.historyCurRecords!, relates: this.curRelatedWords, showRelationStep: this.showRelationStep, silentMode: this.silentMode));
  }

  void firstStepShowWord() {
    this.curShowStep = 1;
    if (currentWord == null) {
      nullShowSetValue();
    } else {
      viewWord!.word!.word = currentWord!.word??"";
      meaningVisible = false;
      viewWord!.word!.meaning = "";
      viewWord!.editable = WordDto.calEditableTool(this.currentWord, this.forceEdit);

      viewCurStudyRecord!.studyLevel = null;
      viewCurStudyRecord!.curStudyLevel = null;
      viewCurStudyRecord!.curEbbinghausStr = null;
      viewCurStudyRecord!.firstJudge = true;
      if (currentStudyRecord != null) {
        viewCurStudyRecord!.noUse = currentStudyRecord!.noUse;
      } else {
        viewCurStudyRecord!.noUse = 1;
      }
      WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
    }
    if (!isClosed)
      emit(HomeState.showWord(word: viewWord!.word, history: historyCurStudyRcdBuffer.historyCurRecords!, relates: this.curRelatedWords, showRelationStep: this.showRelationStep, silentMode: this.silentMode));
  }

  void secondStepShowWord() {
    this.curShowStep = 2;

    if (currentWord == null) {
      nullShowSetValue();
    } else {
      viewWord!.word!.word = currentWord!.word??"";
      viewWord!.word!.meaning = currentWord!.meaning??"";
      meaningVisible = true;
      viewWord!.word!.ownerId = currentWord!.ownerId??"";
      viewWord!.editable = WordDto.calEditableTool(this.currentWord, this.forceEdit);

      WordBiz.shared.copyCommShowValue(currentStudyRecord, viewCurStudyRecord);
      WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
    }
    if (!isClosed)
      emit(HomeState.showWord(word: viewWord!.word, history: historyCurStudyRcdBuffer.historyCurRecords!, relates: this.curRelatedWords, showRelationStep: this.showRelationStep, silentMode: this.silentMode));
  }

  void nullShowSetValue() {
    viewWord!.word!.word = "";
    viewWord!.word!.meaning = "";
    viewWord!.editable = WordDto.calEditableTool(this.currentWord, this.forceEdit);
    viewCurStudyRecord!.studyLevel = 0;
    viewCurStudyRecord!.curStudyLevel = 0;
  }

  remberedBtn(BuildContext context) async {
    await processRemberWord(context);
    zeroStepShowWord();
  }

  forgetBtn(BuildContext context) async {
    await processForgetWord( context);
    zeroStepShowWord();
  }

  blurryBtn(BuildContext context) async {
    await processBlurryWord(context);
    zeroStepShowWord();
  }

  processRemberWord(BuildContext context) async {
    if (currentStudyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.noWordBeingStudied);
      await studyNextWord(context);
      return;
    }
    //克隆一个记录，放在buffer中，以备随机取用，或ReDo的时候取用
    CurStudyRecord tCurStudyRecord = currentStudyRecord!.silentClone();
    tCurStudyRecord.curWord = this.currentWord;
    historyCurStudyRcdBuffer.add(tCurStudyRecord);

    await WordBiz.shared.processRemberWord(currentStudyRecord);

    curStudyRcdBuffer.add2buff(currentStudyRecord!);//放在buffer中，供后面等待时，随机取用

    //清理当前单词相关数据
    cleanVMEnv();
    //取新词
    await studyNextWord(context);
    var a = 0;
  }

  processForgetWord(BuildContext context) async {
    if (currentStudyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.noWordBeingStudied);
      await studyNextWord(context);
      return;
    }

    // 克隆一个记录，放在buffer中，以备随机取用，或ReDo的时候取用
    CurStudyRecord tCurStudyRecord = currentStudyRecord!.silentClone();
    tCurStudyRecord.curWord = currentWord;
    historyCurStudyRcdBuffer.add(tCurStudyRecord);

    await WordBiz.shared.processForgetWord(currentStudyRecord!);

    curStudyRcdBuffer.add2buff(currentStudyRecord!); // 放在buffer中，供后面等待时，随机取用

    // 清理当前单词相关数据
    cleanVMEnv();
    // 取新词
    await studyNextWord(context);
  }

  processBlurryWord(BuildContext context) async {
    if (currentStudyRecord == null) {
      ToastUtil.showToast(LocStr.of(context)!.noWordBeingStudied);
      await studyNextWord(context);
      return;
    }

    // 克隆一个记录，放在buffer中，以备随机取用，或ReDo的时候取用
    CurStudyRecord tCurStudyRecord = currentStudyRecord!.silentClone();
    tCurStudyRecord.curWord = currentWord;
    historyCurStudyRcdBuffer.add(tCurStudyRecord);

    await WordBiz.shared.processBlurryWord(currentStudyRecord!);

    curStudyRcdBuffer.add2buff(currentStudyRecord!); // 放在buffer中，供后面等待时，随机取用

    // 清理当前单词相关数据
    cleanVMEnv();
    // 取新词
    await studyNextWord(context);
  }

  Future<void> refreshBuffer() async {
    await WordBiz.shared.refreshNoUseRcdBuffer(curStudyRcdBuffer);
  }

  Future<void> foldConjunctive(Word? entity) async {
    await searchRelatedWord();
    WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
    if (!isClosed)
      emit(state.clone()..fold(this.curRelatedWords));
  }

  Future<void> unFoldConjunctive(Word? entity) async {
    await searchRelatedWord();
    WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
    if (!isClosed)
      emit(state.clone()..unFold(this.curRelatedWords));
  }

  Future<void> conjunctiveReload(Word? entity) async {
    await searchRelatedWord();
    WordDto.buildStepShowContent4List(this.curRelatedWords, this.curShowStep);
    if (!isClosed)
      emit(state.clone()..unFold(this.curRelatedWords));
  }

  void cleanVMEnv() {
    this.forceEdit = false;
    this.showRelationStep = 0;
    viewWord!.word!.meaning = "";
    //studyWordFragment.fragStudyWordBinding.studyTvMeaning.scrollTo(0,0);
  }

  Future<int> addAllCurStudyRecord() async {
    return await WordBiz.shared.addAllCurStudyRecord();
  }

  void syncHeadDataView() {}

  showByCurStep() {
    switch (this.curShowStep) {
      case 0:
        zeroStepShowWord();
        break;
      case 1:
        firstStepShowWord();
        break;
      case 2:
        secondStepShowWord();
        break;
      default:
        zeroStepShowWord();
    }
  }

  void addWordRelationBtnClicked() {
    bool continueClicked = this.linkBtnClickCounter.continueClicked(DateTime.now().millisecondsSinceEpoch);
    if (continueClicked == true) {
      this.viewWord!.editable = true;
      this.forceEdit = true;
      showByCurStep();
    }
    //playWordSound(this.currentWord);
  }

  void refreshSilentMode() {
    emit(state.clone()..silentMode = this.silentMode);
  }
}
