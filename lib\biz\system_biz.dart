import 'package:words/common/system_config.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/domain/ebbinghaus_point.dart';

import 'package:words/dao/support/db_manager.dart';
import 'package:words/manager/ebbinghaus_point_manager.dart';
import 'package:words/manager/system_manager.dart';
import 'package:words/manager/system_property_manager.dart';
import 'package:words/domain/system_property.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class SystemBiz {

  factory SystemBiz() => _shared();
  static SystemBiz get shared => _shared();
  static SystemBiz? _instance;
  SystemBiz._();
  static SystemBiz _shared() {
    if (_instance == null) {
      _instance = SystemBiz._();
    }
    return _instance!;
  }



  getEbbinghausPoint(String userId, EbbinghausPointType type) async {
    return await SystemManager.shared.getEbbinghausPoint(userId, type);
  }

  Future<Map<String, String>?> getSystemProperties(String id) async {
    List<SystemProperty>? queryList =  await SystemPropertyManager.shared.getSystemProperties(id);
    if (CollectionUtil.isEmpty(queryList))
      return null;
    Map<String, String> result = {};
    queryList?.forEach((element) {
      result[element.propertyName!] = element.propertyValue!;
    });
    return result;
  }

  Future<bool> modifyEbbinghausPoint(EbbinghausPoint ebbinghausPoint) async {
    if (EbbinghausPoint.isValidEbbinghausStr(ebbinghausPoint.pointsStr) != true)
      return false;
    try {
      await EbbinghausPointManager.shared.updateInsert(ebbinghausPoint);
    } catch (e) {
      return false;
    }
    ebbinghausPoint.buildPropertyFromString();
    return true;
  }

  Future<bool> upSertSystemProperties(String? name, String? value, String? userId) async {
    bool result = await DBManager.shared.tbSystemProperty.updateInsert(name, value, userId);
    if (result == true) {
      SystemConfig.setUserProperty(name!, value);
    }
    return result;
  }


}