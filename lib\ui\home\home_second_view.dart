import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/domain/word.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/widget/add_word_to_dict_dialog.dart';
import 'package:words/ui/widget/dialog_util.dart';
import 'package:words/ui/widget/single_word_dialog/common_single_word_btn_callback.dart';
import 'package:words/ui/widget/com_add_word_rel_dialog.dart';
import 'package:words/ui/widget/single_word_dialog/single_word_dialog.dart';


class HomeSecondView extends StatefulWidget {
  HomeSecondView(HomeCubit homeCubit){
  }
  _HomeSecondViewState createState() {
    return _HomeSecondViewState();
  }
}

class _HomeSecondViewState extends State<HomeSecondView> {
  HomeCubit? _homeCubit;
  HomeState? state;
  final GlobalKey _transKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _homeCubit = BlocProvider.of<HomeCubit>(context);
    state = _homeCubit!.state;
    return LayoutBuilder(
      builder: (ctx, constraints) {
        return Padding(
          padding: EdgeInsets.only(top: 0, left: 10, bottom: 10, right: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                  bottomLeft: Radius.circular(10),
                  bottomRight: Radius.circular(10)
              ),
              boxShadow: <BoxShadow>[
                BoxShadow(
                    color: Colors.blue.withOpacity(0.2),
                    offset: Offset(1.1, 1.1),
                    blurRadius: 10.0),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  GestureDetector(
                    onTap: () {
                      _homeCubit!.zeroStepShowWord();
                      _homeCubit!.playWordSound(_homeCubit!.currentWord);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: (_homeCubit!.viewCurStudyRecord!.noUse == 0) ? Colors.lightGreen[400]
                              : ((_homeCubit!.viewCurStudyRecord!.noUse == 1) ? Colors.blue
                              : ((_homeCubit!.viewCurStudyRecord!.noUse == 2) ? Color.fromRGBO(
                              255, 225, 123, 1.0)
                              : ((_homeCubit!.viewCurStudyRecord!.noUse == 3) ? Colors.red[200]
                              : Colors.grey[400]))),
                          borderRadius: BorderRadius.circular(10)
                      ),
                      height: 100,
                      child: Stack(
                        children: [
                          Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 15,
                            child: wordContent(),
                          ),
                          Positioned(
                            top: 5,
                            right: 5,
                            child:
                            GestureDetector(
                              onLongPress: (){
                                _showEditWordDialog(context);
                              },
                              child: _homeCubit!.viewWord!.editable??false ? Icon(Icons.edit, color: Colors.black45,) : Container(),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            child: wordFooter(),
                          )
                        ],
                      ),
                    ),
                  ),
                  Divider(),
                  Stack(
                    children: [
                      Container(
                        height: 160,
                        child: wordTranslation(),
                      ),
                      Positioned(
                          height: 30,
                          right: 10, // 控制浮窗距离右边的位置
                          bottom: 0,    // 控制浮窗距离顶部的位置
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            state!.silentMode == false ? GestureDetector(
                              onTap: () {
                                _homeCubit!.zeroStepShowWord();
                                _homeCubit!.playWordSound(_homeCubit!.currentWord);
                              },
                              onLongPress: () async {
                                _homeCubit!.silentMode = true;
                                _homeCubit!.refreshSilentMode();
                              },
                              child: Icon(Icons.volume_up_outlined, color: Colors.black45,),
                            ) : GestureDetector(
                              onTap: () {
                                _homeCubit!.zeroStepShowWord();
                                _homeCubit!.playWordSound(_homeCubit!.currentWord);
                              },
                              onLongPress: () async {
                                _homeCubit!.silentMode = false;
                                _homeCubit!.refreshSilentMode();
                              },
                              child: Icon(Icons.volume_off_outlined, color: Colors.black45,),
                            ),
                            SizedBox(width: 15,),
                            state!.showRelationStep == 1 ? GestureDetector(
                              onTap: () async {
                                _homeCubit!.showRelationStep = 0;
                                await _homeCubit!.foldConjunctive(_homeCubit!.currentWord);
                              },
                              child: Icon(Icons.unfold_less_sharp, color: Colors.black45,),
                            ) : GestureDetector(
                              onTap: () async {
                                _homeCubit!.showRelationStep = 1;
                                await _homeCubit!.unFoldConjunctive(_homeCubit!.currentWord);
                              },
                              child: Icon(Icons.unfold_more_sharp, color: Colors.black45,),
                            ),
                            SizedBox(width: 15,),
                            GestureDetector(
                              onLongPress: (){
                                _addConjunctive(_homeCubit!.currentWord, null, isActionSheet: false);
                              },
                              onTap: (){
                                _homeCubit!.addWordRelationBtnClicked();
                              },
                              child: Container(
                                height: 40,
                                width: 40,
                                child: Icon(Icons.insert_link, color: Colors.black45,)
                              ),
                            ),
                            SizedBox(width: 10,)
                          ],
                        ),
                      ),
                    ]
                  ),
                  Divider(),
                  Stack(
                    children: <Widget>[
                      relatedWordsContent(),
                      Positioned(
                        // 这是浮窗
                        left: 50,  // 控制浮窗距离左边的位置
                        right: 50, // 控制浮窗距离右边的位置
                        top: 0,    // 控制浮窗距离顶部的位置
                        bottom: 0, // 控制浮窗距离底部的位置
                        child: Column(
                          children: [
                            Container(
                              height: 80,
                              child: GestureDetector(
                                onTap: () {
                                  _homeCubit!.firstStepShowWord();
                                  _homeCubit!.playWordSound(_homeCubit!.currentWord);
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey[200]!, width: 0.5), // 这里设置边框
                                    color: Colors.transparent,
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  _homeCubit!.secondStepShowWord();
                                  _homeCubit!.playWordSound(_homeCubit!.currentWord);
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey[200]!, width: 0.5), // 这里设置边框
                                    color: Colors.transparent, // 设置一个半透明的颜色以便我们可以看到浮窗
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        );
      },
    );
  }

  wordFooter(){
    return Container(
      height: 30,
      child: Row(
        children: [
          SizedBox(width: 5,),
          wordFooterItem(_homeCubit!.viewCurStudyRecord!.studyLevel == null ? "  " : _homeCubit!.viewCurStudyRecord!.studyLevel.toString(), Colors.yellow[700]),
          SizedBox(width: 5,),
          wordFooterItem(_homeCubit!.viewCurStudyRecord!.curEbbinghausStr??"   ", Colors.lightBlue[200]),
          SizedBox(width: 5,),
          wordFooterItem(_homeCubit!.viewCurStudyRecord!.curStudyLevel == null ? "  " : _homeCubit!.viewCurStudyRecord!.curStudyLevel.toString(), (_homeCubit!.viewCurStudyRecord!.firstJudge == true) ? Colors.lightGreen[200] : Color.fromARGB(255, 255, 167, 159)),
          SizedBox(width: 5,),
          wordFooterItem(_homeCubit!.viewCurStudyRecord!.noUse != -1 ? _homeCubit!.wordNextStudyTime! : "", Colors.transparent),
        ],
      ),
    );
  }

  wordFooterItem(String str, Color? bgcolor){
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: bgcolor,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(
        str,
        style: TextStyle(
          color: Colors.black87,
          fontSize: 13
        ),
      ),
    );
  }

  wordContent(){
    return Container(
      child: Center(
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width - 140,
                ),
                child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child:
                    SelectableText(
                        _homeCubit!.viewWord!.word!.word!??"",
                        onTap: (){
                          _homeCubit!.zeroStepShowWord();
                          _homeCubit!.playWordSound(_homeCubit!.currentWord);
                        },
                        style: TextStyle(
                            fontSize: 27,
                            color: Colors.black,
                        ),
                      ),
                ),
              ),
              GestureDetector(
                onLongPress: () {
                  DialogUtil.deleteWordSoundDialog(context,_homeCubit!.currentWord!.word!);
                },
                child: IconButton(
                    alignment: Alignment.centerLeft,
                    iconSize: 20,
                    padding: EdgeInsets.zero,
                    onPressed: (){
                      WordBiz.shared.playWordSound(_homeCubit!.currentWord!.word!);
                    },
                    icon: Icon(Icons.keyboard_voice, color: Colors.blue[800],)
                ),
              ),
            ]
        ),
      )
    );
  }

  // 翻译
  wordTranslation(){
    return Padding(
      key: _transKey,
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: GestureDetector(
        onTap: () {
          _homeCubit!.zeroStepShowWord();
          _homeCubit!.playWordSound(_homeCubit!.currentWord);
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(5)
          ),
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width,
          child: SelectableText(
            _homeCubit!.viewWord!.word!.meaning!??"",
            onTap: (){
              _homeCubit!.zeroStepShowWord();
              _homeCubit!.playWordSound(_homeCubit!.currentWord);
            },
            style: TextStyle(
                fontSize: 16,
                color: Colors.black
            ),
          ),
        ),
      )
    );
  }

  relatedWordsContent(){
    return Container(
        height: 180,
        width: MediaQuery.of(context).size.width,
        child: _relatedWordsContent()
    );
  }

  _relatedWordsContent(){
    _homeCubit!.curRelatedWords = _homeCubit!.curRelatedWords == null ? [] : _homeCubit!.curRelatedWords!;
    //WidgetsBinding.instance.addPostFrameCallback((_) => _scrollController.jumpTo(_scrollController.position.maxScrollExtent));
    _homeCubit!.state.relatedWordList = _homeCubit!.curRelatedWords??[];
    return ListView.builder(
      controller: _scrollController,
      itemCount: state!.relatedWordList!.length??0,
      padding: EdgeInsets.only(top: 0),
      itemBuilder: (ctx, index) {
        return GestureDetector(
          onTap: (){
            _homeCubit!.playWordSound(state!.relatedWordList![index].word);
          },
          onDoubleTapDown: (TapDownDetails details) {
            _homeCubit!.playWordSound(state!.relatedWordList![index].word);
            final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;

            // 获取屏幕宽度
            final double screenWidth = overlay.size.width;
            // 假设菜单宽度为150，你可以根据实际情况调整这个值
            const double menuWidth = 80;
            // 检查右边是否有足够空间
            final bool showOnLeft = (screenWidth - details.globalPosition.dx) < menuWidth;

            // 如果右边空间不足，向左偏移菜单宽度
            final double adjustedX = showOnLeft
                ? details.globalPosition.dx - menuWidth
                : details.globalPosition.dx;

            showMenu(
              context: context,
              position: RelativeRect.fromRect(
                Offset(adjustedX, details.globalPosition.dy) & Size(40, 40),
                Offset.zero & overlay.size,
              ),
              items: <PopupMenuEntry>[
                PopupMenuItem(
                  padding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  enabled: false,
                  height: 20,
                  child: Container (
                    height: 20,
                    alignment: Alignment.center,
                    child: Text(
                      state!.relatedWordList![index].word!.word!,
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                PopupMenuDivider(),
                PopupMenuItem(
                  child: Text(LocStr.of(context)!.details!,),
                  value: "details",
                  onTap: (){
                    _showDetail(state!.relatedWordList![index].word!);
                  },
                ),
                PopupMenuItem(
                  child: Text(LocStr.of(context)!.relate!,),
                  value: "association",
                  onTap: (){
                    _addConjunctive(_homeCubit!.currentWord, state!.relatedWordList![index].word!.word, isActionSheet: false);
                  },
                ),
              ],
            ).then((value) {
              if (value != null) {
                // Handle the selected value
                if (value == "details") {
                  // Handle details action
                } else if (value == "association") {
                  // Handle association action
                }
              }
            });
          },
          onLongPress: () {
            _addConjunctive(_homeCubit!.currentWord, state!.relatedWordList![index].word!.word, isActionSheet: false);
          },
          child: Column(
            children: [
              Container(
                color: _homeCubit!.curShowStep == 0 ? Colors.transparent : state!.relatedWordList![index].relateStep == 0 ? Colors.greenAccent[100] :Colors.transparent,
                padding: EdgeInsets.symmetric(horizontal: 10),
                height: 22,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        state!.relatedWordList![index].showContent??"",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 14,
                            color: Colors.black54
                        ),
                      ),
                    ),
                    SizedBox(width: 10,),
                  ],
                ),
              ),
              SizedBox(height: 2,),
            ]
          ),
        );
      }
    );
  }

  _showDetail(Word entity){
    showDialog(
      context: context,
      builder: (ctx) {
        return Dialog(
          insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
          clipBehavior: Clip.hardEdge,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10)
          ),
          child: SingleWordDialog(entity, CommonSingleWordBtnCallback()),
        );
      }
    );
  }

  _addConjunctive(Word? entity, String? searchString, {bool isActionSheet = true}){
    if (isActionSheet) {
      Navigator.of(context).pop();
    }
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: ComAddWordRelDialog(entity!, searchString, () async {
              await _homeCubit!.conjunctiveReload(_homeCubit!.currentWord);
            })
          );
        }
    );
  }

  Future<void> _showEditWordDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AddWordToDictDialog(word: _homeCubit!.currentWord,
            callBack: () {
                _homeCubit!.showByCurStep();
              });
      },
    );
  }
}