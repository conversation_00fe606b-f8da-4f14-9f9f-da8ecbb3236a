import 'dart:collection';

import 'package:words/commonSupport/utils/string_util.dart';

class ClickCounter {
  int count;
  int clickGap = 300; // 间隔300毫秒
  List<int> clickHistory = [];

  String? flag;

  ClickCounter(this.count);
  bool lastClickEffect() {
    if (clickHistory.length > 1)
      return true;
    else
      return false;
  }

  bool continueClicked(int now) {
    if (now == null) {
      clickHistory.clear();
      return false;
    }
    if (clickHistory.isNotEmpty && now - clickHistory.last > clickGap) {
      clickHistory.clear();
    }
    clickHistory.add(now);
    if (clickHistory.length >= count) {
      clickHistory.clear();
      return true;
    }
    return false;
  }

  bool continueClickedWithFlag(int now, String flag) {
    if (StringUtil.isEmpty(flag)) {
      clickHistory.clear();
      return false;
    }

    if (flag == this.flag) {
      return continueClicked(now);
    } else {
      this.flag = flag;
      clickHistory.clear();
      return continueClicked(now);
    }
  }
}
