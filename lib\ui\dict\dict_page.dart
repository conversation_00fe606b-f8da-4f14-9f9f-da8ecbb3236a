import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/recyclerList/recycler_list_data_handler.dart';
import 'package:words/common/recyclerList/word_content_querier.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/domain/click_counter.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/widget/add_word_to_dict_dialog.dart';
import 'package:words/ui/widget/dialog_util.dart';
import 'package:words/ui/widget/single_word_dialog/common_single_word_btn_callback.dart';
import 'package:words/utils/notification/MyNotification.dart';
import 'package:words/domain/word.dart';
import 'package:words/ui/book/book_content_cubit.dart';
import 'package:words/ui/widget/com_add_word_rel_dialog.dart';
import 'package:words/ui/widget/single_word_dialog/single_word_dialog.dart';

class DictPage extends StatefulWidget {
  String? searchString;
  String? searchString2;
  bool isSearching = false;
  bool searchWaiting = false;
  RecycleListDataHandler? recycleListDataHandler;

  DictPage(){
    WordContentQuerier wordContentQuerier = WordContentQuerier();
    recycleListDataHandler = new RecycleListDataHandler(wordContentQuerier);
  }

  _DictPageState createState() => _DictPageState();
}

class _DictPageState extends State<DictPage> {
  ClickCounter clickCounter = ClickCounter(5);
  List<WordDto> _dataSource = [];

  late EasyRefreshController _controller;
  TextEditingController _wordEditingController = TextEditingController();
  TextEditingController _meanEditingController = TextEditingController();
  FocusNode _wordFocusNode = FocusNode();
  FocusNode _meanFocusNode = FocusNode();
  TextEditingController _confirmDeleteRcdInputController = TextEditingController();

  @override
  void initState() {
    _controller = EasyRefreshController(
        controlFinishLoad: true,
        controlFinishRefresh: true
    );
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    _wordEditingController.dispose();
    _meanEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: Container(
              width: MediaQuery.of(context).size.width,
              height: 30,
              child: Row(
                children: [
                  GestureDetector(
                      child: Icon(Icons.add_circle_outlined),
                      onTap: (){
                        _showAddWordDialog(context);
                      }
                  ),
                  SizedBox(width: 10,),
                  Container(
                      width: 140,
                      height: 30,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5)
                      ),
                      child: TextField(
                        focusNode: _wordFocusNode,
                        controller: _wordEditingController,
                        style: TextStyle(fontSize: 16),
                        maxLines: 1,
                        onChanged: (text) {
                          _searchData();
                        },
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                          hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                          hintText: LocStr.of(context)!.word!,
                          border: OutlineInputBorder(),
                          // filled: true
                        ),
                      )
                  ),
                  SizedBox(width: 5,),
                  Container(
                      width: 140,
                      height: 30,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5)
                      ),
                      child: TextField(
                        focusNode: _meanFocusNode,
                        controller: _meanEditingController,
                        style: TextStyle(fontSize: 16),
                        maxLines: 1,
                        onChanged: (text) {
                          _searchData();
                        },
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                          hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                          hintText: LocStr.of(context)!.meaning!,
                          border: OutlineInputBorder(),
                          // filled: true
                        ),
                      )
                  ),
                  SizedBox(width: 5,),
                  Expanded(child: SizedBox()),
                  GestureDetector(
                    child: Icon(Icons.cancel, color: Colors.grey,),
                    onTap: (){
                      _wordEditingController.clear();
                      _meanEditingController.clear();
                      _wordFocusNode.unfocus();
                      _meanFocusNode.unfocus();
                      _dataSource.clear();
                      setState(() {
                        _dataSource = [];
                      });
                    },
                  ),
                ],
              ),
            )
        ),
        body: _dataSource.length == 0 ? Center(child: Text(""),) : Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: EasyRefresh(
            child: ListView.builder(
              itemCount: _dataSource.length,
              itemBuilder: (BuildContext context, int index) {

                return GestureDetector (
                  onTap: () {
                    WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
                  },
                  onLongPress: (){
                    FocusScope.of(context).unfocus();
                    _showDetail(_dataSource[index].word!);
                  },
                  child: Column(
                    children: [
                    SizedBox(height: 5,),
                    Container(
                        width: MediaQuery.of(context).size.width-30,
                        height: 30,
                        child: Row(
                          children: [
                            Container(
                              width: MediaQuery.of(context).size.width - 140,
                              child: ScrollConfiguration(
                                behavior: ScrollBehavior(),
                                child: GlowingOverscrollIndicator(
                                  axisDirection: AxisDirection.down,
                                  color: Colors.transparent,
                                  showLeading: false, // 禁用顶部溢出效果
                                  showTrailing: false, // 禁用底部溢出效果
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Text(
                                      _dataSource[index].word!.word!,
                                      maxLines: 1,
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: InkWell (
                                highlightColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                onTap: (){
                                  WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
                                },
                                child: Container(),
                              ),
                            ),
                            GestureDetector(
                              onLongPress: () {
                                DialogUtil.deleteWordSoundDialog(context,_dataSource[index]!.word!.word!);
                              },
                              child: Container(
                                width: 30,
                                height: 30,

                                child: IconButton(
                                  alignment: Alignment.centerLeft,
                                  iconSize: 20,
                                  padding: EdgeInsets.zero,
                                  onPressed: (){
                                    WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
                                  },
                                  icon: Icon(Icons.keyboard_voice, color: Colors.blue,)
                              ),

                              ),


                            ),

                            GestureDetector(
                              child: Container(
                                width: 30,
                                height: 30,
                                child: Icon(Icons.link_sharp, color: Colors.grey,),
                              ),
                              onTap: (){
                                _addConjunctive(_dataSource[index].word!, null);
                              },
                              onLongPress: (){
                                _addConjunctive(_dataSource[index].word!, null);
                              },
                            ),
                            SizedBox(width: 10,),
                            FutureBuilder(
                              future: _initBookContentCubitAsync(_dataSource[index]),
                              builder: (BuildContext context, AsyncSnapshot<BookContentCubit> snapshot) {
                                if (snapshot.connectionState == ConnectionState.done) {
                                  return BlocProvider(
                                    create: (ctx) {return snapshot.data!;},
                                    child: BlocBuilder<BookContentCubit, BookContentExistedState>(
                                      builder: (context, state) {
                                        if (state.isExisted == null) {
                                          return Container(
                                            width: 20,
                                            height: 20,
                                          );
                                        } else if (state.isExisted == true) {
                                          return GestureDetector(
                                            child: Container(
                                              width: 20,
                                              height: 20,
                                              child: Icon( Icons.remove_circle_outline , color: Colors.red),
                                            ),
                                            onTap: (){
                                              var continueClicked = clickCounter.continueClickedWithFlag(DateTime.now().millisecondsSinceEpoch, _dataSource[index]!.word!.word!);
                                              if (continueClicked == true) {
                                                forceDeleteStudyRecordDialog(context, _dataSource[index]);
                                              }
                                              else if (!clickCounter.lastClickEffect())
                                                BlocProvider.of<BookContentCubit>(context).addBtnClicked(context, _dataSource[index]);
                                            },
                                          );
                                        } else {
                                          return GestureDetector(
                                            child: Container(
                                              width: 20,
                                              height: 20,
                                              child: Icon(Icons.add_circle_outlined , color: Colors.green),
                                            ),
                                            onTap: (){
                                              BlocProvider.of<BookContentCubit>(context).addBtnClicked(context, _dataSource[index]);
                                            },
                                          );
                                        }
                                      },
                                    ),
                                  );
                                } else {
                                  return Center(
                                    child: Container(),
                                  );
                                }
                              }
                            )
                          ],
                        )
                    ),
                    Container(
                      width: MediaQuery.of(context).size.width-30,
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(5)
                      ),
                      child: Text(_dataSource[index].word!.meaning!),
                    ),
                    SizedBox(height: 5,),
                    Divider()
                  ],
                  )
                );
              },
            ),
            onLoad: _makeData,
            controller: _controller,
          ),
        )
    );
  }

  Future<Null> _makeData() async {
    widget.isSearching = true;
    await widget.recycleListDataHandler!.searchContent(1);
    if (mounted){
      setState(() {
        if (CollectionUtil.nonEmpty(widget.recycleListDataHandler!.itemList)) {
          _dataSource = widget.recycleListDataHandler!
              .itemList!.cast<WordDto>().toList();
        } else {
          _dataSource = [];
        }
      });
    }
    widget.isSearching = false;
    _controller.finishLoad();
  }

  Future<Null> _searchData() async {
    //因为是异步查询，当连续输入时，第一个查询还没完时，后续查询只置searchWaiting标记，多次查询都只记一次，用do循环实现最后一次的补查
    if (widget.isSearching == true) {
      if (widget.searchWaiting == true) {
        return;
      }
      else {
        widget.searchWaiting = true;
        return;
      }
    }
    do {
      widget.isSearching = true;
      widget.searchWaiting = false;
      Map<String, String> param = {};
      param["searchString"] = _wordEditingController.text;
      param["searchString2"] = _meanEditingController.text;
      widget.recycleListDataHandler!.listQueryCallable!.setParam(param);
      widget.recycleListDataHandler!.reset(0);
      await widget.recycleListDataHandler!.searchContent(1);
      if (mounted) {
        setState(() {
          if (widget.recycleListDataHandler!
              .itemList != null) {
            _dataSource = widget.recycleListDataHandler!
                .itemList!.cast<WordDto>().toList();
          } else {
            _dataSource = [];
          }
        });
      };

      //发现连续查询同一张表会卡死，这里睡500毫秒
      await Future.delayed(Duration(milliseconds: 500));
      widget.isSearching = false;
    } while (widget.searchWaiting == true);

    _controller.finishRefresh();
    _controller.finishLoad();
  }

  _showDetail(Word entity){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10)
            ),
            child: SingleWordDialog(entity, CommonSingleWordBtnCallback()),
          );
        }
    );
  }

  _addConjunctive(Word entity, String? searchStr){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: ComAddWordRelDialog(entity, searchStr, (){
              MyNotification(MyNotificationModel(MyNotificationType.kUpdateConjunctive)).dispatch(context);
            }),
          );
        }
    );
  }

  Future<List<WordDto>> convertWordToWordDtoList(List<Word>? words) async {
    List<WordDto>? res = WordBiz.shared.convertWordToWordDtoList(words);
    if (res == null)
      return [];
    else
      return res;
  }

  Future<BookContentCubit> _initBookContentCubitAsync(WordDto dataSource) async {
    final cubit = BookContentCubit();
    while (widget.isSearching == true)
      await Future.delayed(Duration(milliseconds: 100));

    await cubit.checkAddedAndRelated(dataSource);
    return cubit;
  }

  Future<void> _showAddWordDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AddWordToDictDialog(wordStr: _wordEditingController.text,
            meaningStr: _meanEditingController.text,
            callBack: () => _searchData());
      },
    );
  }

  void forceDeleteStudyRecordDialog(BuildContext vContext, WordDto wordDto) {
    showDialog(
      context: context,
      barrierDismissible: false, // 设置点击对话框外部不关闭对话框
      builder: (BuildContext context) {
        // 返回一个 AlertDialog
        return AlertDialog(
          title: Text(LocStr.of(vContext)!.warning),
          content: Container(
              height: 210.0, // 你可以在这里设置你想要的高度
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    Text(LocStr.of(vContext)!.warnDeleteWordStudyRecord(wordDto.word!.word!),
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Text(LocStr.of(vContext)!.inputDeleteToDelete,
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    TextField(
                      controller: _confirmDeleteRcdInputController,
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                        hintText: 'delete',
                        hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: 15),
                  ],
                ),
              )
          ),
          actions: <Widget>[
            // 取消按钮
            TextButton(
              onPressed: () {
                // 在这里处理取消按钮点击事件
                _confirmDeleteRcdInputController.clear();
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.cancel!,),
            ),
            // 确认按钮
            TextButton(
              onPressed: () async {
                if (_confirmDeleteRcdInputController.text == "delete") {
                  int res = await BlocProvider.of<BookContentCubit>(vContext).deleteAllRecordOfWord(wordDto);
                  if (res == 2)
                    ToastUtil.showToast(LocStr.of(context)!.deletedSuccessfully);
                  else
                    ToastUtil.showToast(LocStr.of(context)!.deletionFailed);
                } else {
                  ToastUtil.showToast(LocStr.of(context)!.incorrectInput!,);
                }
                _confirmDeleteRcdInputController.clear();
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.ok!,),
            ),
          ],
        );
      },
    );
  }



}
