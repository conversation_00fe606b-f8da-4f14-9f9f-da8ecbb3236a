import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/ebbinghaus_point.dart';

import 'package:words/domain/word.dart';

class EbbinghausPointManager {
  //单例模式
  factory EbbinghausPointManager() => _shared();
  static EbbinghausPointManager get shared => _shared();
  static EbbinghausPointManager? _instance;
  EbbinghausPointManager._();
  static EbbinghausPointManager _shared() {
    if (_instance == null) {
      _instance = EbbinghausPointManager._();
    }
    return _instance!;
  }

  Future<bool> updateInsert(EbbinghausPoint ebbinghausPoint) async {
    if (ebbinghausPoint == null)
      return false;

    if (StringUtil.isEmpty(ebbinghausPoint.id)) {
      ebbinghausPoint.id = UuidUtil.getUuid();
      if (StringUtil.isEmpty(ebbinghausPoint.userId) == null)
        ebbinghausPoint.userId = SystemConfig.user!.id;

      if (ebbinghausPoint.pointType == null)
        return false;
      await DBManager.shared.tbEbbinghausPoint.insert(ebbinghausPoint);
    } else {
      var entityInDb = await DBManager.shared.tbEbbinghausPoint.baseQueryById(ebbinghausPoint.id!);
      if (entityInDb != null) {
        await DBManager.shared.tbEbbinghausPoint.baseUpdateById(ebbinghausPoint.id!, ebbinghausPoint.toJson());
      } else {
        await DBManager.shared.tbEbbinghausPoint.insert(ebbinghausPoint);
      }
    }
    return true;

  }

  

}