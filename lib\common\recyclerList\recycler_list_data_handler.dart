import 'package:words/common/recyclerList/list_query_callable.dart';

import '../../commonSupport/utils/collection_util.dart';

//检索数据和显示数据可能不是一个类型，T是检索数据，E是显示数据
class RecycleListDataHandler {
  static final int PAGE_SIZE = 20;
  int curStart = 0;  // 列表中的第一条在数据库中的位置
  int curLast = 0; // 列表中的最后一条在数据库中的位置
  int pageSize = PAGE_SIZE;
  int scrollPos = 0; // 页面上显示的当前记录在数据显示列表中的位置
  List? itemList;
  ListQueryCallable? listQueryCallable;

  RecycleListDataHandler(ListQueryCallable listQueryCallable) {
    this.listQueryCallable = listQueryCallable;
  }

  void reset(int target) {
    curStart = target;
    curLast = target;
    pageSize = PAGE_SIZE;
    scrollPos = 0;
    itemList = null;
  }

  //direction -1，从当前位置，向后查pageSize个。1：从当前位置，向前查pageSize个。0：开始位置清0，查找pageSize个记录
  Future<List?> searchContent(int direction) async {
    int begin;
    int actPageSize;

    if (direction == 0) {
      reset(0);
      begin = curLast;
      actPageSize = pageSize;
    } else if (direction == 1) { //向下刷一页
      begin = curLast;
      actPageSize = pageSize;
      if ((this.itemList != null) && (this.itemList!.length > 0))
        scrollPos = this.itemList!.length - 1;
      else
        scrollPos = 0;
    } else { //向上刷一页
      begin = curStart;
      actPageSize = pageSize;
    }

    List? queryRes = null;
    if ((direction == 0) || (direction == 1)) {
      queryRes = await queryByPage(begin, actPageSize); //获取指定条数的数据，支持过滤掉一部分数据
    }
    else {
      queryRes = await reversQueryByPage(begin, actPageSize); //反向获取指定条数的数据，支持过滤掉一部分数据
    }

    if (direction == -1) {
      if (CollectionUtil.isEmpty(queryRes))
        scrollPos = 0;
      else
        scrollPos = queryRes!.length;
    }

    if (CollectionUtil.isEmpty(queryRes))
      return itemList;

    if ((direction == 0) || (direction == 1)){
      itemList = listQueryCallable!.combineList(itemList, await listQueryCallable!.inflateData(queryRes));
    } else {
      itemList = listQueryCallable!.combineList(await listQueryCallable!.inflateData(queryRes), itemList);
    }

    return itemList;
  }

  Future<List> queryByPage(int begin, int actPageSize) async {
    List validContents = [];
    List? searchRes;
    int tStart = begin;
    int tLast = begin;

    do {
      searchRes = await this.listQueryCallable!.queryByPage(tLast, actPageSize);

      if (CollectionUtil.isEmpty(searchRes))
        break;

      tLast = tLast + searchRes!.length;

      List? tmp = await this.listQueryCallable!.filterContent(searchRes);
      validContents.addAll(tmp!);

    } while ((validContents.length < actPageSize) && (CollectionUtil.nonEmpty(searchRes)));

    if (CollectionUtil.isEmpty(validContents) || (validContents.length <= actPageSize))
      ;
    else
      validContents = validContents.getRange(0, actPageSize).toList();

    this.curLast = tLast;

    return validContents;
  }

  Future<List?> reversQueryByPage(int begin, int pageSize) async {
    if (begin == 0) {
      return null;
    }

    List validContents = [];
    List? searchRes;
    int curStart = begin;
    int curLast = begin;
    int actPageSize = pageSize;

    do {
      actPageSize = curStart - pageSize > 0 ? pageSize : curStart;
      curStart = curStart - pageSize > 0 ? curStart - pageSize: 0;

      searchRes = await this.listQueryCallable!.queryByPage(curStart, actPageSize);

      List? tRemovedRes = await this.listQueryCallable!.filterContent(searchRes);
      if (CollectionUtil.nonEmpty(tRemovedRes)) {
        if (validContents.length > 0)
          tRemovedRes!.addAll(validContents);
        validContents = tRemovedRes!;
      }
    } while ((validContents.length < actPageSize)
        && (curStart > 0));

    if (CollectionUtil.nonEmpty(validContents) && (validContents.length > actPageSize))
      validContents = validContents.getRange(0, actPageSize).toList();

    this.curStart = curStart;

    return validContents;
  }


}
