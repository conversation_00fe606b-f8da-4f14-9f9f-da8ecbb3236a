import 'dart:convert';

import 'package:words/commonSupport/utils/collection_util.dart';

class StringUtil {
  StringUtil();

  static String? normalString(String s) {
    return nonEmptyTrimmedOrNull(s);
  }

  static bool nonEmpty(String? s) {
    return s != null && s.isNotEmpty;
  }

  static bool isEmpty(String? s) {
    return s == null || s.isEmpty;
  }

  static bool nonWhitespaceString(String? s) {
    return s != null && s.trim().isNotEmpty;
  }

  static String? nonEmptyOrNull(String? s) {
    return nonEmpty(s) ? s : null;
  }

  static String nonNullOrBlank(String? s) {
    return s == null ? "" : s;
  }

  static List<String>? strToList(String? vStr, [String vSeparator = '\r\n,']) {
    if (nonEmpty(vStr)) {
      List<String>? tResult = trimList(vStr!.split(vSeparator));
      return tResult;
    }
    return null;
  }

  static String? joinString(List<String>? vString, String vSeparator) {
    if (vString == null || vString.isEmpty) return null;
    return vString.where(nonEmpty).join(vSeparator);
  }

  static String? nonEmptyTrimmedOrNull(String? s) {
    String? out = s;
    if (out != null) {
      out = out.trim();
      out = out.isEmpty ? null : out;
    }
    return out;
  }

  static List<int> getUTF8Bytes(String s) {
    try {
      return utf8.encode(s);
    } on Exception catch (e) {
      print(e);
      throw Exception('UTF-8 is an unsupported encoding?!?');
    }
  }

  static final List<String> EMPTY_STRING_ARRAY = <String>[];

  static List<String>? removeNullInList(List<String> vList) {
    if (vList == null) return null;

    vList.removeWhere((String t) => !nonEmpty(t));
    return vList;
  }

  static List<String>? trimList(List<String>? vList) {
    if (vList == null) return null;

    return vList.map((String t) => t?.trim() ?? t).toList();
  }

  static String upperFirstChar(String vStr) {
    return vStr.trim().substring(0, 1).toUpperCase() + vStr.substring(1);
  }

  static String toSqlWhereString(List<String> vStrList) {
    StringBuffer buf = StringBuffer();
    buf.write("");
    if (CollectionUtil.nonEmpty(vStrList)) {
      vStrList.forEach((element) {buf.write("'${element.replaceAll("'", "''")}',");});
    }
    String result = buf.toString();
    result = result.substring(0, result.length - 1) + "";
    return result;
  }
}
