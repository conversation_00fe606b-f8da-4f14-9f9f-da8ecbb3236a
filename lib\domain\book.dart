import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import 'package:words/domain/support/is_json_serialable.dart';

part 'book.g.dart';

@JsonSerializable()
class Book implements IsJsonSerialable{
	@Json<PERSON>ey(name: "ID")
	late String id;

	@<PERSON><PERSON><PERSON>ey(name: "NAM<PERSON>")
	late String name;

	@<PERSON><PERSON><PERSON>ey(name: "<PERSON>WNER")
	late String owner;

	@Json<PERSON>ey(name: "SEQUENC<PERSON>")
	late int sequence;

	Book();

	factory Book.fromJson(Map<String, dynamic> json) => _$BookFromJson(json);

	@override
	Map<String, dynamic> toJson() => _$BookToJson(this);

	@override
	fromJson(param) {
		return Book.fromJson(param);
	}

	@override
	String toString() {
		return jsonEncode(this);
	}


}