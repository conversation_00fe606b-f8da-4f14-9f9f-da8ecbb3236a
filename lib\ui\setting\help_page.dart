import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:webview_flutter/webview_flutter.dart';

class HelpPage extends StatefulWidget {
  _HelpPageState createState() => _HelpPageState();
}

class _HelpPageState extends State<HelpPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocStr.of(context)!.help!),
      ),
      body: FutureBuilder<String>(
        future: loadLocalHelpPage(),
        builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasData) {
              String htmlData = snapshot.data!;

              var webViewController = WebViewController()
                ..setJavaScriptMode(JavaScriptMode.unrestricted)
                ..setBackgroundColor(const Color(0x00000000))
                ..setNavigationDelegate(
                  NavigationDelegate(
                    onProgress: (int progress) {
                      // Update loading bar.
                    },
                    onPageStarted: (String url) {},
                    onPageFinished: (String url) {},
                    onHttpError: (HttpResponseError error) {},
                    onWebResourceError: (WebResourceError error) {},
                    onNavigationRequest: (NavigationRequest request) {
                      if (request.url.startsWith('https://www.youtube.com/')) {
                        return NavigationDecision.prevent;
                      }
                      return NavigationDecision.navigate;
                    },
                  ),
                )
                ..loadRequest(Uri.parse(Uri.dataFromString(htmlData, mimeType: 'text/html', encoding: Encoding.getByName('utf-8')).toString()));


              return WebViewWidget( controller:  webViewController );
            } else
              return Container();
          } else {
            return Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  Future<String> loadLocalHelpPage() async {
    String languageCode = Localizations.localeOf(context).languageCode;
    String data;
    switch(languageCode) {
      case 'zh':
        data = await rootBundle.loadString('assets/help/help_zh.html');
        break;
      default:
        data = await rootBundle.loadString('assets/help/help_en.html');
        break;
    }
    //加载图片
    String study_1_Image = await loadAssetAsBase64('assets/help/images/study_1.jpg');
    data = data.replaceAll('images/study_1.jpg', 'data:image/jpg;base64,${study_1_Image}');

    return data;
  }



  Future<String> loadAssetAsBase64(String path) async {
    ByteData data = await rootBundle.load(path);
    Uint8List bytes = data.buffer.asUint8List();
    return base64Encode(bytes);
  }


}
