import 'dart:async';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/system_config.dart';
import 'package:flutter/material.dart';
import 'package:words/domain/statistics_count_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class GeneralStatisticsPage extends StatefulWidget {
  GeneralStatisticsPage();

  _GeneralStatisticsPageState createState() => _GeneralStatisticsPageState();
}

class _GeneralStatisticsPageState extends State<GeneralStatisticsPage> {
  List<StatisticsCountDto> _dataSource = [ ];
  int _start = 0;
  int _pageSize = 30;
  bool isSearching = false;

  EasyRefreshController? _refreshController;

  @override
  void initState() {
    _refreshController = EasyRefreshController(
        controlFinishLoad: true,
        controlFinishRefresh: true
    );
    super.initState();
    _footerLoadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(""),
      ),
      body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              Expanded(
                child: EasyRefresh(
                  child: _dataSource.length > 0 ? showList() : Center(child: Text(""),),
                  onLoad: _footerLoadData,
                  controller: _refreshController,
                ),
              )
            ],
          ),
        )
    );
  }

  Future<void> _footerLoadData() async {
    isSearching = true;
    List<StatisticsCountDto>? queryResult = await WordBiz.shared.queryCommingCountByPage(SystemConfig.user!.id, _start, _pageSize);
    if (CollectionUtil.nonEmpty(queryResult)) {
      setState(() {
        _dataSource.addAll(queryResult!);
      });
      _start += queryResult?.length ?? 0;
    }
    isSearching = false;
    _refreshController?.finishLoad();
  }

  showList() {
    return ListView.builder(
      itemCount: _dataSource.length,
      itemBuilder: (context, index) {
        // 使用交替的背景颜色
        final backgroundColor = index % 2 == 0 
            ? Colors.grey[100] // 偶数行的背景色
            : Colors.white;    // 奇数行的背景色

        return Container(
          color: backgroundColor,
          child: ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_dataSource[index].date ?? ''),
                Text(_dataSource[index].count?.toString() ?? ''),
              ],
            ),
          ),
        );
      },
    );
  }
}
