// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ebbinghaus_point.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EbbinghausPoint _$EbbinghausPointFromJson(Map<String, dynamic> json) =>
    EbbinghausPoint()
      ..id = json['ID'] as String?
      ..userId = json['USER_ID'] as String?
      ..pointTypeStr = json['POINT_TYPE_STR'] as String?
      ..pointsStr = json['POINTS_STR'] as String?;

Map<String, dynamic> _$EbbinghausPointToJson(EbbinghausPoint instance) =>
    <String, dynamic>{
      'ID': instance.id,
      'USER_ID': instance.userId,
      'POINT_TYPE_STR': instance.pointTypeStr,
      'POINTS_STR': instance.pointsStr,
    };
