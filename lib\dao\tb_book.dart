import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/book.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class TbBook extends DBBaseProvider<Book> {

  @override
  String get tableName => "BOOK";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID text not null,
        NAME text,
        OWNER text,
        SEQUENCE int
        )
        ''';
  }

  queryList(int page) async {
    List<Map<String, Object?>>? list = await baseQueryList(page);
    List<Book> result = [];
    if (CollectionUtil.nonEmpty(list)) {
      list!.forEach((element) {
        Book entity = Book.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

}