import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/domain/word.dart';
import 'package:words/utils/other/UserInfo.dart';

class TbStudyRecord extends DBBaseProvider {

  @override
  String get tableName => "STUDY_RECORD";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID text not null,
        CONTENT_SEARCH_ID text,
        USER_ID text,
        STUDY_LEVEL int,
        FIRST_STUDY_DATE int,
        LAST_STUDY_DATE int,
        MIN_NEXT_STUDY_DATE int,
        NEXT_STUDY_DATE int,
        NEVER_SEEN int,
        ADD_DATE int,
        CUR_EBBINGHAUS_STR text,
        CONTENT_ID text
        )
        ''';
  }

  queryList(int page) async {
    List<Map<String, Object?>>? list = await baseQueryList(page);
    List<StudyRecord> result = [];
    if (list != null && list.length > 0) {
      list.forEach((element) {
        StudyRecord entity = StudyRecord.fromJson(Map<String, dynamic>.of(element));
        result.add(entity);
      });
    }
    return result;
  }

  Future<bool> wordStudyRecordExisted(String? contentId) async {
    if (StringUtil.isEmpty(contentId))
      return false;
    final Database? db = await dataBase;
    List<Map<String, Object?>>? list = await db?.query(tableName, where: "CONTENT_ID = ?", whereArgs: [contentId!]);
    return (list?.length ?? 0) > 0;
  }

  wordInsert(Word entity) async {
    StudyRecord record = StudyRecord();
    record.id = Uuid().v4().toLowerCase().replaceAll("-", "");
    record.contentSearchId = entity.searchId!;
    record.userId = UserInfo.kTestUserId;
    record.studyLevel = 0;
    DateTime d = DateTime.now();
    record.firstStudyDate = d;
    record.lastStudyDate = d;
    record.minNextStudyDate = d;
    record.nextStudyDate = d;
    record.neverSeen = false;
    record.addDate = d;
    record.curEbbinghausStr = "25d";
    record.contentId = entity.id;
    return await baseInsert(record.toJson());
  }

  wordDelete(String contentId) async {
    final Database? db = await dataBase;
    return await db?.delete(tableName, where: "CONTENT_ID = ?", whereArgs: [contentId]);
  }


}