import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/book.dart';

import 'package:words/domain/cur_study_record.dart';

class TbCurStudyRecord extends DBBaseProvider<CurStudyRecord> {

  @override
  String get tableName => "CUR_STUDY_RECORD";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        CONTENT_SEARCH_ID TEXT,
        USER_ID TEXT,
        STUDY_LEVEL INTEGER,
        FIRST_STUDY_DATE INTEGER,
        LAST_STUDY_DATE INTEGER,
        MIN_NEXT_STUDY_DATE INTEGER,
        NEXT_STUDY_DATE INTEGER,
        NEVER_SEEN INTEGER,
        CUR_STUDY_LEVEL INTEGER,
        FIRST_JUDGE INTEGER,
        ADD_DATE INTEGER,
        ORI_STUDY_LEVEL INTEGER,
        ORI_NEXT_STUDY_DATE INTEGER,
        ORI_MIN_NEXT_STUDY_DATE INTEGER,
        CUR_ADJ_STUDY_LEVEL INTEGER,
        BACK_NEXT_STUDY_DATE INTEGER,
        CUR_EBBINGHAUS_STR TEXT,
        CONTENT_ID TEXT,
        PRIMARY KEY (ID)
        )
        ''';
  }


}