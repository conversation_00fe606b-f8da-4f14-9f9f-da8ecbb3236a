import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:words/biz/book_biz.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/recyclerList/book_content_querier.dart';
import 'package:words/common/recyclerList/recycler_list_data_handler.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/domain/book.dart';
import 'package:words/domain/book_mark.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/widget/dialog_util.dart';
import 'package:words/ui/widget/single_word_dialog/common_single_word_btn_callback.dart';
import 'package:words/domain/word.dart';
import 'package:words/ui/book/book_content_cubit.dart';
import 'package:words/utils/notification/MyNotification.dart';
import 'package:words/ui/widget/com_add_word_rel_dialog.dart';
import 'package:words/ui/widget/single_word_dialog/single_word_dialog.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class BookContent extends StatefulWidget {
  String? jumpNumber;
  BookContentQuerier? bookContentQuerier;
  Book? book;
  bool hideStudied = false;
  bool isSearching = false;
  RecycleListDataHandler? recycleListDataHandler;


  BookContent(Book book){
    this.book = book;
    bookContentQuerier = BookContentQuerier(this.book!.id, this.hideStudied);
    recycleListDataHandler = RecycleListDataHandler(bookContentQuerier!);
  }

  _BookContentState createState() => _BookContentState();
}

class _BookContentState extends State<BookContent> {

  List<WordDto> _dataSource = [];
  EasyRefreshController? _refreshController;
  int _length = 0;
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _editingController = TextEditingController();
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener = ItemPositionsListener.create();
  int currentFirstIndex = 0;

  @override
  void initState() {
    _refreshController = EasyRefreshController(
        controlFinishLoad: true,
        controlFinishRefresh: true
    );
    _editingController.addListener(() {
      setState(() {
        _length = _editingController.text.length;
      });
    });

    _itemPositionsListener.itemPositions.addListener(() {
      if (_itemPositionsListener.itemPositions.value.isNotEmpty) {
        currentFirstIndex = _itemPositionsListener.itemPositions.value.first.index;
      }
    });
    super.initState();
    _footerLoadData();
  }

  @override
  void dispose() {
    _refreshController?.dispose();
    _editingController.dispose();
    _focusNode.unfocus();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              bookContentApBar(),
              Expanded(
                child: EasyRefresh(
                  child: _dataSource.length > 0 ? wordList() : Center(child: Text(""),),
                  onLoad: _footerLoadData,
                  onRefresh: _headerLoadData,
                  controller: _refreshController,
                ),
              )
            ],
          ),
        )
    );
  }

  bookContentApBar(){
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 90+MediaQuery.of(context).padding.top,
      color: Colors.blue,
      child: Stack(
        children: [
          Positioned(
            top: MediaQuery.of(context).padding.top,
            width: MediaQuery.of(context).size.width,
            height: 50,
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  alignment: Alignment.centerLeft,
                  child: IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: (){
                        Navigator.pop(context);
                      },
                      icon: Icon(Icons.arrow_back_ios_new,color: Colors.white,)
                  ),
                ),
                Expanded(
                  child: Text(
                    widget.book!.name,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18
                    ),
                  ),
                ),
                SizedBox(width: 50,)
              ],
            ),
          ),
          Positioned(
            left: 15,
            right: 15,
            bottom: 10,
            height: 30,
            child: Row(
              children: [
                Container(
                  width: 150,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5)
                  ),
                  child: TextField(
                    focusNode: _focusNode,
                    controller: _editingController,
                    keyboardType: TextInputType.number,
                    style: TextStyle(fontSize: 16),
                    maxLines: 1,
                    onSubmitted: (text) {
                      int index = int.parse(text);
                      _jumpTo(index);
                      // _itemScrollController.jumpTo(index: index);
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                      hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                      hintText: LocStr.of(context)!.bookmark,
                      suffixIconColor: Colors.grey,
                      suffixIcon: GestureDetector(
                        child: Icon(Icons.cancel),
                        onTap: (){
                          _editingController.clear();
                          FocusScope.of(context).unfocus();
                        },
                      ),
                      suffixIconConstraints: BoxConstraints(
                          minWidth: 0
                      )
                      // filled: true
                    ),
                  ),
                ),
                SizedBox(width: 15,),
                GestureDetector(
                    child:    IconButton(
                      constraints: BoxConstraints(
                          maxHeight: 30,
                          maxWidth: 30
                      ),
                      padding: EdgeInsets.zero,
                      icon: Icon(Icons.directions_run_outlined, color: Colors.white,),
                      onPressed: (){
                        int inputNum = 0;
                        try {
                          inputNum = int.parse(_editingController.text);
                        } catch (e) {
                          return;
                        }
                        FocusScope.of(context).unfocus();
                        _jumpTo(inputNum);
                        _editingController.clear();
                      },
                    )
                ),
                SizedBox(width: 10,),
                GestureDetector(
                  onLongPress: () {
                    _moveBookMarkConfirm(_dataSource[currentFirstIndex].sequence!);
                  },
                  child:    IconButton(
                    constraints: BoxConstraints(
                      maxHeight: 30,
                      maxWidth: 30
                    ),
                    padding: EdgeInsets.zero,
                    icon: Icon(Icons.bookmark_add_outlined, color: Colors.white,),
                    onPressed: () async {
                      await _getBookMark();
                    },
                  )
                ),
                SizedBox(width: 10,),
                IconButton(
                  constraints: BoxConstraints(
                      maxHeight: 30,
                      maxWidth: 30
                  ),
                  padding: EdgeInsets.zero,
                  icon: widget.hideStudied ? Icon(Icons.unfold_more_sharp, color: Colors.white,) : Icon(Icons.unfold_less_sharp, color: Colors.black45,),
                  onPressed: (){
                    toggleHideBtnClicked();
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  wordList(){
    return ScrollablePositionedList.builder(
      itemScrollController: _itemScrollController,
      itemCount: _dataSource.length,
      itemPositionsListener: _itemPositionsListener,
      itemBuilder: (BuildContext context, int index) {
        return GestureDetector (
          onTap: () {
            WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
          },
          onLongPress: (){
            FocusScope.of(context).unfocus();
            _wordItemLongPressed(_dataSource[index]);
          },
          child:
            Column(
              children: [
                SizedBox(height: 5,),
                Container(
                  width: MediaQuery.of(context).size.width-30,
                  height: 30,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width - 140,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "[${_dataSource[index].sequence}] ",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                              ),
                            ),
                            Expanded(
                              child: ScrollConfiguration(
                                behavior: ScrollBehavior(),
                                child: GlowingOverscrollIndicator(
                                  axisDirection: AxisDirection.down,
                                  color: Colors.transparent,
                                  showLeading: false, // 禁用顶部溢出效果
                                  showTrailing: false, // 禁用底部溢出效果
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Text(_dataSource[index].word!.word!,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(child: Container()),
                      GestureDetector(
                        onLongPress: () {
                          DialogUtil.deleteWordSoundDialog(context,_dataSource[index]!.word!.word!);
                        },
                        child: Container(
                          width: 30,
                          height: 30,
                          child: IconButton(
                            alignment: Alignment.centerLeft,
                            iconSize: 20,
                            padding: EdgeInsets.zero,
                            onPressed: (){
                              WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
                            },
                            icon: Icon(Icons.keyboard_voice, color: Colors.blue,)
                          ),
                        ),
                      ),
                      GestureDetector(
                        child: Container(
                          width: 30,
                          height: 30,
                          child: Icon(Icons.link_sharp, color: Colors.grey,),
                        ),
                        onTap: (){
                          _addConjunctive(_dataSource[index], null);
                        },
                        onLongPress: (){
                          _addConjunctive(_dataSource[index], null);
                        },
                      ),
                      SizedBox(width: 10,),
                      FutureBuilder(
                        future: _initBookContentCubitAsync(_dataSource[index]),
                        builder: (BuildContext context, AsyncSnapshot<BookContentCubit> snapshot) {
                          if (snapshot.connectionState == ConnectionState.done) {
                            return BlocProvider(
                              create: (ctx) {return snapshot.data!;},
                              child: BlocBuilder<BookContentCubit, BookContentExistedState>(
                                builder: (context, state) {
                                  BlocProvider.of<BookContentCubit>(context)
                                      .checkAddedAndRelated(_dataSource[index]);
                                  if (state.isExisted == null) {
                                    return Container(
                                      width: 20,
                                      height: 20,
                                    );
                                  } else if (state.isExisted == true) {
                                    return
                                      GestureDetector(
                                        child: Container(
                                          width: 20,
                                          height: 20,
                                          child: Icon(
                                              Icons.remove_circle_outline, color: Colors.red),
                                        ),
                                        onTap: () {
                                          BlocProvider.of<BookContentCubit>(context)
                                              .addBtnClicked(context, _dataSource[index]);
                                        },
                                      );
                                  } else {
                                    return GestureDetector(
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        child: Icon(
                                            Icons.add_circle_outlined, color: Colors.green),
                                      ),
                                      onTap: () {
                                        BlocProvider.of<BookContentCubit>(context).addBtnClicked(context,
                                            _dataSource[index]);
                                      },
                                    );
                                  }
                                },
                              ),
                            );
                          }else{
                            return Center(
                              child: Container(),
                            );
                          }
                        }
                      )
                    ],
                  )
                ),
                Container(
                  width: MediaQuery.of(context).size.width-30,
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(5)
                  ),
                  child: Text(_dataSource[index].word!.meaning == null ? "":_dataSource[index].word!.meaning!),
                ),
                SizedBox(height: 5,),
                Divider()
              ],
        ),

        );
      },
    );
  }

  Future<Null> _headerLoadData() async {
    widget.isSearching = true;
    await widget.recycleListDataHandler!.searchContent(-1).then((value){
      if (widget.recycleListDataHandler!.curStart == 0) {
        _refreshController?.finishRefresh(IndicatorResult.noMore);
      }else{
        _refreshController?.finishRefresh();
      }
      if (mounted) {
        setState(() {
          _dataSource = widget.recycleListDataHandler!.itemList!.cast<WordDto>().toList();
        });
      }
      if (widget.recycleListDataHandler!.scrollPos > 0) {
        _itemScrollController.jumpTo(index: widget.recycleListDataHandler!.scrollPos);
      }
    });
    widget.isSearching = false;
    _refreshController?.finishRefresh();
  }

  Future<Null> _footerLoadData() async {
    widget.isSearching = true;
    await widget.recycleListDataHandler!.searchContent(1).then((value){
      if (mounted) {
        setState(() {
          _dataSource = widget.recycleListDataHandler!.itemList!.cast<WordDto>().toList();
        });
      }
    });
    widget.isSearching = false;
    _refreshController?.finishLoad();
  }

  Future<Null> _jumpTo(int row) async {
    widget.recycleListDataHandler!.reset(row - 1);
    await widget.recycleListDataHandler!.searchContent(1).then((value){
      _refreshController?.finishLoad();
      _refreshController?.finishRefresh();
      if (mounted) {
        setState(() {
          _dataSource = widget.recycleListDataHandler!.itemList!.cast<WordDto>().toList();
        });
      }
      _itemScrollController.jumpTo(index: widget.recycleListDataHandler!.scrollPos);
    });
    _refreshController?.finishLoad();
    _refreshController?.finishRefresh();
  }

  _showDetail(Word entity){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10)
            ),
            child: SingleWordDialog(entity, CommonSingleWordBtnCallback()),
          );
        }
    );
  }

  _moveBookMarkConfirm(int index){
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // 返回一个 AlertDialog
        return AlertDialog(
          title: Text(LocStr.of(context)!.hint!),
          content: Text(LocStr.of(context)!.confirmMoveBookmark(index.toString())),
          actions: <Widget>[
            // 取消按钮
            TextButton(
              onPressed: () {
                // 在这里处理取消按钮点击事件
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.cancel!),
            ),
            // 确认按钮
            TextButton(
              onPressed: () async {
                await moveBookMarkToPosition(widget.book!.id, index);
                // 在这里处理确认按钮点击事件
                Navigator.of(context).pop(); // 关闭弹出窗口
              },
              child: Text(LocStr.of(context)!.ok!),
            ),
          ],
        );
      },
    );
  }

  _addConjunctive(WordDto wordDto, String? searchStr){
    if (wordDto.isInDict != null && wordDto.isInDict == false) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotInDict);
      return;
    }
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: ComAddWordRelDialog(wordDto!.word!, searchStr, (){
              MyNotification(MyNotificationModel(MyNotificationType.kUpdateConjunctive)).dispatch(context);
            }),
          );
        }
    );
  }

  Future<BookContentCubit> _initBookContentCubitAsync(WordDto dataSource) async {
    final cubit = BookContentCubit();
    while (widget.isSearching == true)
      await Future.delayed(Duration(milliseconds: 100));

    await cubit.checkAddedAndRelated(dataSource);
    return cubit;
  }

  moveBookMarkToPosition(String bookId, int bookMarkPostion) async {
    return BookBiz.shared.moveBookMarkToPostion(bookId, bookMarkPostion, SystemConfig.user!.id);
  }

  _getBookMark() async {
    BookMark? bookMark = await BookBiz.shared.getBookMark(widget.book!.id, SystemConfig.user!.id);
    if (bookMark == null) {
      ToastUtil.showToast(LocStr.of(context)!.bookmarkNotAdded);
      return;
    }
    _editingController.text = bookMark.curSequence.toString();
  }

  void toggleHideBtnClicked() {
    widget.hideStudied = !widget.hideStudied;
    widget.bookContentQuerier!.hideStudied = widget.hideStudied;

    int realPosInDb = _dataSource[currentFirstIndex].sequence!;
    if (realPosInDb == null)
      return;
    _jumpTo(realPosInDb);

  }

  void _wordItemLongPressed(WordDto wordDto) {
    if (wordDto.isInDict != null && wordDto.isInDict == false) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotInDict);
      return;
    }
    _showDetail(wordDto.word!);
  }
}
