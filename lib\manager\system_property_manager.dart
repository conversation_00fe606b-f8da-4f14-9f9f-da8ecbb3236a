import 'package:words/dao/support/db_manager.dart';

import 'package:words/domain/system_property.dart';
import 'package:words/domain/word.dart';

class SystemPropertyManager {
  //单例模式
  factory SystemPropertyManager() => _shared();
  static SystemPropertyManager get shared => _shared();
  static SystemPropertyManager? _instance;
  SystemPropertyManager._();
  static SystemPropertyManager _shared() {
    if (_instance == null) {
      _instance = SystemPropertyManager._();
    }
    return _instance!;
  }

  Future<List<SystemProperty>?> getSystemProperties(String userId) async {
    return await DBManager.shared.tbSystemProperty.getSystemProperty(userId);
  }

  

}