import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:meta/meta.dart';
import 'package:words/Manager/word_manager.dart';
import 'package:words/Manager/word_relation_manager.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/recyclerList/book_content_querier.dart';
import 'package:words/common/recyclerList/list_query_callable.dart';
import 'package:words/common/recyclerList/recycler_list_data_handler.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/book.dart';
import 'package:words/domain/book_content.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/domain/word.dart';
import 'package:words/domain/word_relation.dart';
import 'package:words/dto/word_dto.dart';

import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/l10n/loc_str.dart';
part 'book_content_state.dart';

class BookContentCubit extends Cubit<BookContentExistedState> {

  Word? curWord;

  BookContentCubit() : super(BookContentExistedState().init());
  BookContentCubit.withWord(Word? curWord) : super(BookContentExistedState().init()){
    this.curWord = curWord;
  }

  checkAddedAndRelated(WordDto wordDto) async {
    if (wordDto == null) {
      emit(state.clone()
        ..isExisted = false
        ..isRelated = false);
      return;
    }
    if (!isClosed) {
      emit(state.clone()
        ..isExisted = wordDto.added
        ..isRelated = wordDto.related);
    }
  }

  Future<bool> addBtnClicked(BuildContext context, WordDto wordDto) async {
    if (wordDto.isInDict != null && wordDto.isInDict == false) {
      ToastUtil.showToast(LocStr.of(context)!.wordNotInDict);
      return false;
    }
    StudyRecord? studyRecord = await WordBiz.shared.queryStudyRecordByContendId(wordDto.word!.id);
    CurStudyRecord? curStudyRecord = await WordBiz.shared.queryCurStudyRcdByContendId(wordDto.word!.id);

    if (studyRecord != null) {
      if (! WordBiz.shared.isWordStudyStarted(studyRecord, curStudyRecord)) { //未开始学习，删除记录
        await WordBiz.shared.deleteBothStudyRecord(studyRecord, curStudyRecord);
        //刷新显示
        if (!isClosed) {
          wordDto.added = false;
          emit(state.clone()..isExisted = wordDto.added);
        }
      } else {
        ToastUtil.showToast(LocStr.of(context)!.wordStartedCantDelete);
      }
    } else {
      int res = await WordBiz.shared.addStrangeWordToRecord(wordDto.word);
      if (res > 0) {
        //刷新显示
        if (!isClosed) {
          wordDto.added = true;
          emit(state.clone()..isExisted = wordDto.added);
        }
      }
    }
    return true;
  }

  deleteRelationBtn(BuildContext context, Word sourceEntity, WordDto wordDto, int index) async {
    int res = await toggleWordRelation(context, sourceEntity, wordDto!.word!);
    if (res == 0)
      return;
    wordDto!.related = false;
    if (!isClosed) {
      emit(state.clone()..isRelated = false..operationIndex = index);
    }
  }

  addRelationBtn(BuildContext context, Word curWord, WordDto relateWordDto, int index)  async {
    int res = await toggleWordRelation(context,curWord, relateWordDto!.word!);
    if (res == 0)
      return;
    relateWordDto!.related = true;
    if (!isClosed) {
      emit(state.clone()..isRelated = true..operationIndex = index);
    }
  }

  Future<int> toggleWordRelation(BuildContext context, Word curWord, Word relateWord) async {
    if (curWord == null || relateWord == null)
      return 0;
    if (StringUtil.isEmpty(curWord.id) || StringUtil.isEmpty(relateWord.id))
      return 0;

    if(curWord.id == relateWord.id) {
      ToastUtil.showToast(LocStr.of(context)!.cantRelateToItself);
      return 0;
    }
    //取word相关的relation，
    List<WordRelation>? allRelations = await WordRelationManager.shared.queryRelationForWordPair(curWord, relateWord);

    //如果不存在，则插入
    if (CollectionUtil.isEmpty(allRelations)) {
      WordRelation firstRel = new WordRelation();
      firstRel.id = UuidUtil.getUuid();
      firstRel.relationId = curWord.id;
      firstRel.wordId = relateWord.id;
      firstRel.ownerId = SystemConfig.user!.id;

      List<WordRelation> newRels = []..add(firstRel);
      await addWordRelationList(newRels);
      return 1;
    } else {//如果存在，则删除
      await deleteWordRelationList(allRelations);
      return -1;
    }
  }
  Future<int> addWordRelationList(List<WordRelation> relationList) async {
    return await WordBiz.shared.addMultipleWordRelation(relationList);
  }

  Future<int> addWordRelation(WordRelation relation) async {
    return await WordBiz.shared.addWordRelation(relation);
  }

  Future<int> deleteWordRelationList(List<WordRelation>?  relations) async {
    if (CollectionUtil.isEmpty(relations))
      return 0;
    return await WordBiz.shared.deleteWordRelationList(relations);
  }

  Future<int> deleteRelation(WordRelation? relation) async {
    if (relation == null)
      return 0;
    return await WordBiz.shared.deleteWordRelation(relation);
  }

  Future<int> deleteAllRecordOfWord(WordDto wordDto) async {
    try {
      await WordBiz.shared.deleteAllRecordOfWord(wordDto.word!);
      //刷新显示
      if (!isClosed) {
        wordDto.added = false;
        emit(state.clone()..isExisted = wordDto.added);
      }
      return 2;
    } catch (err) {
      return -1;
    }
  }
}
