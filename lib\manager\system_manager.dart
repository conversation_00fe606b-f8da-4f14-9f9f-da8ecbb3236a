import 'package:words/dao/support/db_manager.dart';

import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/domain/word.dart';

class SystemManager {
  //单例模式
  factory SystemManager() => _shared();
  static SystemManager get shared => _shared();
  static SystemManager? _instance;
  SystemManager._();
  static SystemManager _shared() {
    if (_instance == null) {
      _instance = SystemManager._();
    }
    return _instance!;
  }

  getEbbinghausPoint(String userId, EbbinghausPointType type) async {
    return await DBManager.shared.tbEbbinghausPoint.getEbbinghausPoint(userId, type);
  }
}