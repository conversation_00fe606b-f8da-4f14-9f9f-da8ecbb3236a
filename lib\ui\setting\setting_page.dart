import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/app_constants.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/setting/restore_setting_page.dart';
import 'package:words/utils/router/word_router.dart';

class SettingPage extends StatefulWidget {
  _SettingPageState createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  StreamSubscription?  _intentDataStreamSubscription;

  List<WordDto> _dataSource = [];

  @override
  void initState() {
    super.initState();
    listenShareMediaFiles(context);
  }

  @override
  void dispose() {
    if (_intentDataStreamSubscription != null)
      _intentDataStreamSubscription!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocStr.of(context)!.setting!),
        centerTitle: true, // 确保标题居中
      ),
      body: ListView(
        children: ListTile.divideTiles( // 这会在每个 ListTile 之间添加分隔线
          context: context,
          tiles: [
            ListTile(
              title: Text(LocStr.of(context)!.generalSetting!),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                Navigator.pushNamed(context, WordRouter.generalSetting);
              },
            ),
            ListTile(
              title: Text(LocStr.of(context)!.shareToBackupData!),
              trailing: Icon(Icons.chevron_right),
              onTap: () async {
                await WordBiz.shared.shareBackupDb(context);
              },
            ),
            ListTile(
              title: Text(LocStr.of(context)!.help!),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                Navigator.pushNamed(context, WordRouter.helpPage);
              },
            ),
          ],
        ).toList(),
      ),
    );
  }

  // All listeners to listen Sharing media files & text
  void listenShareMediaFiles(BuildContext context) {
    // For sharing images coming from outside the app
    // while the app is in the memory
    _intentDataStreamSubscription = ReceiveSharingIntent.instance.getMediaStream().listen((List<SharedMediaFile> value) {
      navigateToShareMedia(context, value);
    }, onError: (err) {
      debugPrint("$err");
    });

    // For sharing images coming from outside the app while the app is closed
    ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> value) {
      navigateToShareMedia(context, value);
    });

  }

  void navigateToShareMedia(BuildContext context, List<SharedMediaFile> value) {
    if (value.isNotEmpty) {
      var newFiles = <File>[];
      value.forEach((element) {
        newFiles.add(File(
          Platform.isIOS ?
                  element.type == SharedMediaType.file ?
                      element.path.toString().replaceAll(AppConstants.replaceableText, "")
                      : element.path
                  : element.path,
        ));
      });
      Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => RestoreSettingPage(
            files: newFiles,
          )));
    }
  }

}
