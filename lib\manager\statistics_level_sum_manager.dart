import 'package:words/common/system_config.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/statistics_level_sum.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/commonSupport/utils/date_util.dart';
import 'package:words/common/system_config.dart';
import 'package:words/common/system_const.dart';
import 'package:words/domain/word.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';

class StatisticsLevelSumManager {
  //单例模式
  factory StatisticsLevelSumManager() => _shared();
  static StatisticsLevelSumManager get shared => _shared();
  static StatisticsLevelSumManager? _instance;
  StatisticsLevelSumManager._();
  static StatisticsLevelSumManager _shared() {
    if (_instance == null) {
      _instance = StatisticsLevelSumManager._();
    }
    return _instance!;
  }

  Future<List<StatisticsLevelSum>?> queryByUseridStudyLvEbbstr(CurStudyRecord curStudyRecord, String userId, int statStudyLev, DateTime time) async {
    List<dynamic>? queryRes = await DBManager.shared.tbStatisticsLevelSum.commQueryList(
      where: 'USER_ID = ? '
          'AND STUDY_LEVEL = ? '
          'AND NEVER_SEEN = ? '
          'AND START_STAT_TIME > ? '
          'AND DURATION_STR = ? '
      ,
      whereArgs: [userId, statStudyLev, curStudyRecord.neverSeen == true ? 1 : 0, time.millisecondsSinceEpoch - SystemConfig.STATISTICS_PERIOD,
        StringUtil.isEmpty(curStudyRecord.curEbbinghausStr) ?
          SystemConfig.repeatEbbinghausPoint!.pointStrList![statStudyLev]
          : curStudyRecord.curEbbinghausStr],
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;

    List<StatisticsLevelSum> result = [];
    queryRes!.forEach((element) {
      StatisticsLevelSum entity = StatisticsLevelSum.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });

    return result;
  }

  void addNewStatisticsLevelSum(StatisticsLevelSum statisticsLevelSum) {
    statisticsLevelSum.id = UuidUtil.getUuid();
    return DBManager().tbStatisticsLevelSum.insert(statisticsLevelSum);
  }

  void updateStatisticsLevelSumById(StatisticsLevelSum statisticsLevelSum) {
    return update(statisticsLevelSum);
  }
  update(StatisticsLevelSum? statisticsLevelSum) async {
    return DBManager.shared.tbStatisticsLevelSum.baseUpdateById(statisticsLevelSum!.id!, statisticsLevelSum.toJson());
  }

}