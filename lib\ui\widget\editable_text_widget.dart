import 'package:flutter/material.dart';

class EditableTextWidget extends StatefulWidget {
  int? maxlines = 1;
  String? initText = "";
  Future<bool> Function(String)? confirmFunc;
  EditableTextWidget({this.initText, this.maxlines, this.confirmFunc});

  @override
  _EditableTextWidgetState createState() => _EditableTextWidgetState();
}

class _EditableTextWidgetState extends State<EditableTextWidget> {
  bool _isEditable = false;
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    this._controller.text = widget.initText??"";
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: TextField(
            controller: _controller,
            enabled: _isEditable,
            keyboardType: TextInputType.multiline,
            minLines: 1, // 正常情况下显示的行数
            maxLines: widget.maxlines
          ),
        ),
        _ShowIcon(),
      ],
    );
  }

  _ShowIcon() {
    if (_isEditable == false) {
      return IconButton(
        icon: Icon(Icons.edit),
        onPressed: () {
          setState(() {
            _isEditable = true;
          });
        },
      );
    } else {
      return Row(children: [
        IconButton(
          icon: Icon(Icons.check),
          onPressed: () async {
            bool result = true;
            if (widget.confirmFunc != null) {
              result = await widget.confirmFunc!(_controller.text);
            }
            if (result == true) {
              setState(() {
                _isEditable = false;
              });
            }
          },
        ),
        IconButton(
          icon: Icon(Icons.close),
          onPressed: () {
            setState(() {
              _isEditable = false;
              _controller.text = widget.initText??"";
            });
          },
        ),
      ]);
    }
  }
}
