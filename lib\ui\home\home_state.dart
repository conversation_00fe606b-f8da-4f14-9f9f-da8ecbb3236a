part of 'home_cubit.dart';

class HomeState {

  int showRelationStep = 0;
  bool silentMode = true;
  Word? viewWord;
  List<WordDto>? relatedWordList;

  List<CurStudyRecord>? lastCurStudyRcds;

  HomeState({this.showRelationStep = 1, this.relatedWordList = const <WordDto>[]});

  fold(List<WordDto>? items) {
    this.showRelationStep = 0;
    this.relatedWordList = items??[];
  }

  unFold(List<WordDto>? items) {
    this.showRelationStep = 1;
    this.relatedWordList = items??[];
  }

  HomeState.showWord({Word? word, List<CurStudyRecord>? history, List<WordDto>? relates, int? showRelationStep, bool? silentMode}){
    if (word == null)
      return;
    this.showRelationStep = showRelationStep??0;
    this.viewWord = word;
    this.relatedWordList = relates??[];
    this.lastCurStudyRcds = history;
    this.silentMode = silentMode??true;
  }

  HomeState clone() {
    return HomeState()
      ..showRelationStep = this.showRelationStep
      ..silentMode = this.silentMode
      ..viewWord = this.viewWord
      ..relatedWordList = this.relatedWordList
      ..lastCurStudyRcds = this.lastCurStudyRcds;
  }
}