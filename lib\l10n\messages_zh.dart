// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(
    String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'zh';

  static m0(word) => "确认删除 ${word} 的语音文件吗？";

  static m1(number) => "确认把书签移到 ${number} 吗?";

  static m2(placehold) => "下载语音Url，单词用 ${placehold} 代替.";

  static m3(placehold) => "无效URL，URL中必包含 ${placehold} 标记";

  static m4(msg) => "修改失败，请检查 ${msg} ";

  static m5(number) => "已添加 ${number} 个新词.?";

  static m6(word) => "删除 \"${word}\" 的学习记录，将会清除这个单词的所有学习历史！";

  @override
  final Map<String, dynamic> messages = _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
      'addNewWord': MessageLookupByLibrary.simpleMessage('添加新单词'),
    'addSuccess': MessageLookupByLibrary.simpleMessage('添加成功!'),
    'autoDownloadVoice': MessageLookupByLibrary.simpleMessage('自动下载语音'),
    'book': MessageLookupByLibrary.simpleMessage('课本'),
    'bookmark': MessageLookupByLibrary.simpleMessage('书签'),
    'bookmarkNotAdded': MessageLookupByLibrary.simpleMessage('本书尚未添加书签，长按书签图标添加书签.'),
    'cancel': MessageLookupByLibrary.simpleMessage('取消'),
    'cantRelateToItself': MessageLookupByLibrary.simpleMessage('不能对自己关联.'),
    'click': MessageLookupByLibrary.simpleMessage('点击'),
    'clickToBackup': MessageLookupByLibrary.simpleMessage('点击这里，通过分享备份'),
    'close': MessageLookupByLibrary.simpleMessage('关闭'),
    'confirmDeleteWordVoice': m0,
    'confirmMoveBookmark': m1,
    'defaultToMute': MessageLookupByLibrary.simpleMessage('默认静音'),
    'deletedSuccessfully': MessageLookupByLibrary.simpleMessage('删除成功!'),
    'deletionFailed': MessageLookupByLibrary.simpleMessage('删除失败，请重试!'),
    'details': MessageLookupByLibrary.simpleMessage('详情'),
    'dictionary': MessageLookupByLibrary.simpleMessage('词典'),
    'downloadVoice': MessageLookupByLibrary.simpleMessage('下载语音'),
    'downloadVoiceUrlHint': m2,
    'editWord': MessageLookupByLibrary.simpleMessage('编辑单词'),
    'forgot': MessageLookupByLibrary.simpleMessage('忘记'),
    'generalSetting': MessageLookupByLibrary.simpleMessage('通用设置'),
    'help': MessageLookupByLibrary.simpleMessage('帮助'),
    'hint': MessageLookupByLibrary.simpleMessage('提示'),
    'incorrectInput': MessageLookupByLibrary.simpleMessage('输入错误'),
    'initReciteSet': MessageLookupByLibrary.simpleMessage('首次背诵周期设置'),
    'inputDeleteToDelete': MessageLookupByLibrary.simpleMessage('如果确认删除，请输入 delete \"delete\"'),
    'inputToConfirmRestore': MessageLookupByLibrary.simpleMessage('如果确认还原数据，请输入 \"restore\".'),
    'invalidDownloadUrl': m3,
    'invalidWordOrMeaning': MessageLookupByLibrary.simpleMessage('无效的单词或词义!'),
    'meaning': MessageLookupByLibrary.simpleMessage('词义'),
    'modifyFailedCheck': m4,
    'modifySuccess': MessageLookupByLibrary.simpleMessage('修改成功.'),
    'nWordAdded': m5,
    'newWordFirst': MessageLookupByLibrary.simpleMessage('生词优先'),
    'noNewWordsToAdd': MessageLookupByLibrary.simpleMessage('无新词可添加!'),
    'noWordBeingStudied': MessageLookupByLibrary.simpleMessage('无正在学习的单词!'),
    'ok': MessageLookupByLibrary.simpleMessage('确定'),
    'recallPeriod': MessageLookupByLibrary.simpleMessage('记忆周期设置'),
    'recallPeriodHint': MessageLookupByLibrary.simpleMessage('如果您不了解本项配置的含义，请不要修改配置！'),
    'relate': MessageLookupByLibrary.simpleMessage('关联'),
    'remember': MessageLookupByLibrary.simpleMessage('记得'),
    'restartNow': MessageLookupByLibrary.simpleMessage('立刻重启'),
    'restoreData': MessageLookupByLibrary.simpleMessage('还原数据'),
    'restoreFailCopyError': MessageLookupByLibrary.simpleMessage('恢复失败：拷贝数据出错，请重新恢复.'),
    'restoreFailInvalidFile': MessageLookupByLibrary.simpleMessage('恢复失败：不是合法数据文件，请检查数据文件.'),
    'restoreFailNoFile': MessageLookupByLibrary.simpleMessage('恢复失败：没有收到共享数据文件.'),
    'restoreFailPlsRetry': MessageLookupByLibrary.simpleMessage('Restoration failed, please restore again.'),
    'restoreSuccess': MessageLookupByLibrary.simpleMessage('恢复成功！请重启app.'),
    'restoreWarning': MessageLookupByLibrary.simpleMessage('警告：还原数据会覆盖您原有的学习记录数据！请务必备份好原有数据！'),
    'reviewIntervalSet': MessageLookupByLibrary.simpleMessage('复习间隔周期设置'),
    'setting': MessageLookupByLibrary.simpleMessage('设置'),
    'shareToBackupData': MessageLookupByLibrary.simpleMessage('通过分享备份数据'),
    'study': MessageLookupByLibrary.simpleMessage('学习'),
    'studyRecordUpdated': MessageLookupByLibrary.simpleMessage('复习记录已更新.'),
    'title': MessageLookupByLibrary.simpleMessage('欢迎使用 Words'),
    'uncertain': MessageLookupByLibrary.simpleMessage('模糊'),
    'warnDeleteWordStudyRecord': m6,
    'warning': MessageLookupByLibrary.simpleMessage('警告'),
    'word': MessageLookupByLibrary.simpleMessage('单词'),
    'wordNotInDict': MessageLookupByLibrary.simpleMessage('单词不在词典中，请先加入词典.'),
    'wordNotStarted': MessageLookupByLibrary.simpleMessage('单词尚未开始学习.'),
    'wordStartedCantDelete': MessageLookupByLibrary.simpleMessage('单词已开始学习，不能删除.')
  };
}
