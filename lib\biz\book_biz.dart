
import 'package:words/domain/book_content.dart';
import 'package:words/domain/book_mark.dart';
import 'package:words/manager/book_content_manager.dart';
import 'package:words/manager/book_mark_manager.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

import 'package:words/manager/study_record_manager.dart';
import 'package:words/domain/study_record.dart';

class BookBiz {

  factory BookBiz() => _shared();
  static BookBiz get shared => _shared();
  static BookBiz? _instance;
  BookBiz._();
  static BookBiz _shared() {
    if (_instance == null) {
      _instance = BookBiz._();
    }
    return _instance!;
  }

  Future<List<BookContent>?> searchBookContentByPage(String? bookId, int? begin, int? pageSize) async {
    return await BookContentManager.shared.searchBookContentByPage(bookId, begin, pageSize);
  }

  Future<List<BookContent>?> filterBookContentByStudied(List<BookContent>? bookContens, bool hideStudied) async {
    if (hideStudied == false)
      return bookContens;

    if (CollectionUtil.isEmpty(bookContens))
      return null;

    List<String>? searchIds = BookContent.convert2ContentSearchIdList(bookContens!);
    List<StudyRecord>? studyRecords = await StudyRecordManager.shared.queryByContentSearchIdList(searchIds);

    Map<String, StudyRecord>? studyRecordMap = StudyRecord.buildContentSearchIdIndexedMapFromList(studyRecords);

    List<BookContent> result = [];
    for (BookContent content in bookContens) {
      if (studyRecordMap == null) {
        result.add(content);
      } else if (studyRecordMap![content.contentSearchId] == null) {
        result.add(content);
      }
    }
    return result;
  }

  moveBookMarkToPostion(String bookId, int bookMarkPostion, String userId) async {
    await BookMarkManager.shared.moveBookMarkToPostion(bookId, bookMarkPostion, userId);
    return true;
  }

  Future<BookMark?> getBookMark(String bookId, String userId) async {
    return await BookMarkManager.shared.getBookMarkPostion(bookId, userId);
  }

}