import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import 'package:words/commonSupport/utils/type_serial_util.dart';

part 'statistics_level_sum.g.dart';

@JsonSerializable()
class StatisticsLevelSum {
	@J<PERSON><PERSON><PERSON>(name: "ID")
	String? id;

	@<PERSON><PERSON><PERSON><PERSON>(name: "USER_ID")
	String? userId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "STUDY_LEVEL")
	int? studyLevel;

	@<PERSON><PERSON><PERSON><PERSON>(name: "DURATION_STR")
	String? durationStr;

	@<PERSON><PERSON><PERSON><PERSON>(name: "REMBER_SUM")
	int? remberSum;

	@<PERSON><PERSON><PERSON><PERSON>(name: "FORGET_SUM")
	int? forgetSum;

	@<PERSON><PERSON><PERSON><PERSON>(name: "BLURRY_SUM")
	int? blurrySum;

	@J<PERSON><PERSON><PERSON>(name: "LAST_UPDATE_TIME", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? lastUpdateTime;

	@<PERSON><PERSON><PERSON><PERSON>(name: "POINT_CHG_TIME", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? pointChgTime;

	@JsonKey(name: "NEVER_SEEN", fromJson: TypeSerialUtil.dynamicToBool, toJson: TypeSerialUtil.boolToInt)
	bool? neverSeen;

	@JsonKey(name: "START_STAT_TIME", fromJson: TypeSerialUtil.dynamicToDateTime, toJson: TypeSerialUtil.dateTimeToInt)
	DateTime? startStatTime;

	StatisticsLevelSum();

	StatisticsLevelSum.fromParam(
			{required this.id,
				required this.userId,
				required this.studyLevel,
				required this.durationStr,
				required this.remberSum,
				required this.forgetSum,
				required this.blurrySum,
				required this.lastUpdateTime,
				required this.pointChgTime,
				required this.neverSeen,
				required this.startStatTime});

	factory StatisticsLevelSum.fromJson(Map<String, dynamic> json) => _$StatisticsLevelSumFromJson(json);

	Map<String, dynamic> toJson() => _$StatisticsLevelSumToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}