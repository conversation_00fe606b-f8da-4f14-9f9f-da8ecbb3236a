import 'dart:io';

import 'package:words/domain/ebbinghaus_point.dart';
import 'package:words/domain/user.dart';

class SystemConfig {
  static const String DB_RELATIVE_PATH = "databases";
  static const String SOUND_RELATIVE_PATH = "sound";
  static const String LOG_RELATIVE_PATH = "log";

  static const String ANDROID_BASE_STORAGE_PATH = "/storage/emulated/0";
  static const String ANDROID_DB_BASE_PATH = ANDROID_BASE_STORAGE_PATH + "/01-work/repeatMem/" + DB_RELATIVE_PATH;
  static const String ANDROID_SOUND_BASE_PATH = ANDROID_BASE_STORAGE_PATH + "/01-work/repeatMem/" + SOUND_RELATIVE_PATH;
  static const String ANDROID_LOG_PATH = ANDROID_BASE_STORAGE_PATH + "/01-work/repeatMem/" + LOG_RELATIVE_PATH;

  static const String IOS_PACKAGE_PATH = "com.woods.words";

  static String DB_BASE_PATH = "";
  static String SOUND_BASE_PATH = "";
  static String LOG_PATH = "";
  static String FULL_DB_PATH = "";

  static bool initiated = false;
  static const String DB_NAME = "repeat_mem.db";

  static const DEBUG_DB = false;

  static const DB_USER = "repeatMem";

  static const STATISTICS_PERIOD = 90 * 24 * 60 * 60 * 1000; // 90天

  static const DOUBLE_CLICK_INTERVAL = 200; // 双击间隔300毫秒

  static User? user;

  static User? systemUser;

  static EbbinghausPoint? repeatEbbinghausPoint;

  static EbbinghausPoint? studyEbbinghausPoint;

  static Map<String, String?>? sysProperties = {};

  static Map<String, String?>? userProperties = {};

  static String? getUserProperty(String propertyName) {
    return userProperties![propertyName];
  }

  static String? getSysProperty(String propertyName) {
    return sysProperties![propertyName];
  }

  static void setUserProperty(String propertyName, String? propertyValue) {
    userProperties![propertyName] = propertyValue;
  }

  static void setSysProperty(String propertyName, String? propertyValue) {
    sysProperties![propertyName] = propertyValue;
  }
  static List<String> getUserIdLists() {
    return [
      systemUser!.id,
      user!.id,
    ];
  }

}