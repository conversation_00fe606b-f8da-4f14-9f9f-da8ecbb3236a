//检索数据和显示数据可能不是一个类型，T是检索数据，E是显示数据
import 'package:words/commonSupport/utils/collection_util.dart';

abstract class ListQueryCallable {
  Future<List?> queryByPage(int? begin, int? pageSize);
  Future<List?> filterContent(List? searchRes);
  Future<List?> inflateData(List? sourceList); //为了效率，查询过滤后再填充应用数据的工具
  void setParam(Object param);
  List? combineList(List? first, List? second) {
		if (CollectionUtil.isEmpty(first) && CollectionUtil.isEmpty(second))
			return null;
		List result = [];

		if (CollectionUtil.isEmpty(first)) {
			result.addAll(second!);
			return result;
		}
		if (CollectionUtil.isEmpty(second)) {
			result.addAll(first!);
			return result;
		}

		result.addAll(first!);
		result.addAll(second!);
		return result;
	}
}