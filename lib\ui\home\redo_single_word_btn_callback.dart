
import 'package:words/ui/widget/single_word_dialog/single_word_btn_callable.dart';

import 'package:words/biz/word_biz.dart';
import 'package:words/domain/cur_study_record.dart';

class ReDoSingleWordBtnCallback implements SingleWordBtnCallable {
  void processRemberBtn(CurStudyRecord curStudyRecord) {
    WordBiz.shared.processRemberWord(curStudyRecord);
  }
  void processForgetBtn(CurStudyRecord curStudyRecord) {
    WordBiz.shared.processForgetWord(curStudyRecord);
  }

  void processBlurryBtn(CurStudyRecord curStudyRecord) {
    WordBiz.shared.processBlurryWord(curStudyRecord);
  }
}
