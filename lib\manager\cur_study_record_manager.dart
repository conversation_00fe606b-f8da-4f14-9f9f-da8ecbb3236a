import 'dart:collection';

import 'package:words/common/system_config.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/cur_study_rcd_buffer.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/date_util.dart';

import 'package:words/domain/support/has_string_properties.dart';
import 'package:words/domain/word.dart';
import 'package:words/commonSupport/utils/string_util.dart';

class CurStudyRecordManager {
  //单例模式
  factory CurStudyRecordManager() => _shared();
  static CurStudyRecordManager get shared => _shared();
  static CurStudyRecordManager? _instance;
  CurStudyRecordManager._();
  static CurStudyRecordManager _shared() {
    if (_instance == null) {
      _instance = CurStudyRecordManager._();
    }
    return _instance!;
  }

  Future<int> calRemainExpiredCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
      where: 'USER_ID = ? '
          'AND ORI_STUDY_LEVEL > 0 '
          'AND ORI_NEXT_STUDY_DATE <= ?',
      whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch]
    );
    return res;
  }
  Future<int> calRemainExpiredStartedCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: 'USER_ID = ? '
            'AND ORI_STUDY_LEVEL > 0 '
            'AND ORI_NEXT_STUDY_DATE <= ? '
            'AND FIRST_JUDGE = 0 ',
        whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch]
    );
    return res;
  }

  calRemainNewCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL = 0',
        whereArgs: [SystemConfig.user!.id]
    );
    return res;
  }

  calRemainNewStartedCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL = 0 AND CUR_STUDY_LEVEL != 0',
        whereArgs: [SystemConfig.user!.id]
    );
    return res;
  }

  calRemainTodayCurStudyRcd(DateTime now) async {
    DateTime todayBorder = DateUtil.getTodayBoundary();
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL > 0 '
            ' AND ORI_MIN_NEXT_STUDY_DATE <= ? '
            ' AND ORI_NEXT_STUDY_DATE > ? '
            ' AND ORI_NEXT_STUDY_DATE < ?',
        whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch, now.millisecondsSinceEpoch, todayBorder.millisecondsSinceEpoch]
    );
    return res;
  }
  calRemainTodayStartedCurStudyRcd(DateTime now) async {
    DateTime todayBorder = DateUtil.getTodayBoundary();
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL > 0 '
            ' AND ORI_MIN_NEXT_STUDY_DATE <= ? '
            ' AND ORI_NEXT_STUDY_DATE > ? '
            ' AND ORI_NEXT_STUDY_DATE < ? '
            ' AND FIRST_JUDGE = 0 ',
        whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch, now.millisecondsSinceEpoch, todayBorder.millisecondsSinceEpoch]
    );
    return res;
  }

  calRemainCommingCurStudyRcd(DateTime now) async{
    int nowMills = now.millisecondsSinceEpoch;
    int todayBorder = DateUtil.getTodayBoundary().millisecondsSinceEpoch;
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: ' USER_ID = ? AND ORI_STUDY_LEVEL > 0 '
            ' AND ORI_MIN_NEXT_STUDY_DATE < ? '
            ' AND ORI_NEXT_STUDY_DATE >= ? ',
        whereArgs: [SystemConfig.user!.id, nowMills, todayBorder]
    );
    return res;
  }


  calRemainCommingStartedCurStudyRcd(DateTime now) async{
    int nowMills = now.millisecondsSinceEpoch;
    int todayBorder = DateUtil.getTodayBoundary().millisecondsSinceEpoch;
    var res = await DBManager.shared.tbCurStudyRecord.commCount(
        where: ' USER_ID = ? AND ORI_STUDY_LEVEL > 0 '
            ' AND ORI_MIN_NEXT_STUDY_DATE < ? '
            ' AND ORI_NEXT_STUDY_DATE >= ? '
            ' AND FIRST_JUDGE = 0 ',
        whereArgs: [SystemConfig.user!.id, nowMills, todayBorder]
    );
    return res;
  }
  Future<List<CurStudyRecord>?> queryStartedRecord(String userId, DateTime now) async {
    int nowMills = now.millisecondsSinceEpoch;
    List<dynamic>? queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
      where: 'USER_ID = ? '
          'AND (CUR_STUDY_LEVEL != 0 OR STUDY_LEVEL != 0) '
          'AND NEXT_STUDY_DATE <= ? ',
      whereArgs: [userId, nowMills],
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;
    return queryRes!.cast<CurStudyRecord>().toList();
  }


  Future<List<CurStudyRecord>?> getStartedExpiredRecord (String userId, DateTime now) async {
    int nowMills = now.millisecondsSinceEpoch;
    var queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
        where: 'USER_ID = ? '
            'AND (CUR_STUDY_LEVEL != 0 OR STUDY_LEVEL != 0) '
            'AND NEXT_STUDY_DATE < ?',
        whereArgs: [userId, nowMills]
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;
    return queryRes!.cast<CurStudyRecord>().toList();
  }

  Future<List<CurStudyRecord>?> getStartedNotExpiredRecord(String userId, DateTime now) async {
    int nowMills = now.millisecondsSinceEpoch;
    var queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
        where: 'USER_ID = ? '
            'AND (CUR_STUDY_LEVEL != 0 OR STUDY_LEVEL != 0) '
            'AND NEXT_STUDY_DATE > ? '
            'AND LAST_STUDY_DATE <= ?',
        whereArgs: [userId, nowMills, nowMills],
        orderBy: 'Next_STUDY_DATE ASC',
        limit: 1
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;

    return queryRes!.cast<CurStudyRecord>().toList();
  }

  Future<List<CurStudyRecord>?> queryNotStartedRecord(String userId) async {
    var queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
        where: 'USER_ID = ? '
            'AND CUR_STUDY_LEVEL = 0 '
            'AND STUDY_LEVEL = 0 ',
        whereArgs: [userId],
        orderBy: 'Next_STUDY_DATE DESC',
        limit: 1
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;

    return queryRes!.cast<CurStudyRecord>().toList();
  }

  Future<List<CurStudyRecord>?> queryRemainCommingRecord(String userId, DateTime now) async {
    int nowMills = now.millisecondsSinceEpoch;
    var queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
        where: 'USER_ID = ? '
            'AND (CUR_STUDY_LEVEL != 0 OR STUDY_LEVEL != 0)'
            'AND MIN_NEXT_STUDY_DATE <= ?',
        whereArgs: [userId, nowMills]
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;
    return queryRes!.cast<CurStudyRecord>().toList();
  }

  deleteCurStudyRecord(CurStudyRecord? currentStudyRecord) async {
    if (currentStudyRecord == null)
      return 0;
    await DBManager.shared.tbCurStudyRecord.baseDeleteById(currentStudyRecord.id!);
  }

  refreshNoUseRcdBuffer(CurStudyRcdBuffer curStudyRcdBuffer) async {
    int nowMills = DateTime.now().millisecondsSinceEpoch;
    var queryRes = await DBManager.shared.tbCurStudyRecord.commQueryEntityList(CurStudyRecord(),
      orderBy: 'NEXT_STUDY_DATE ASC',
      where: 'USER_ID = ? '
            'AND MIN_NEXT_STUDY_DATE < ?',
        whereArgs: [SystemConfig.user!.id, nowMills]
    );

    if (CollectionUtil.isEmpty(queryRes))
      return;
    for (CurStudyRecord record in queryRes!) {
      curStudyRcdBuffer.add2buff(record);
    }

    curStudyRcdBuffer.bufferIndex = 0;
  }

  deleteById(String? id) {
    if (StringUtil.isEmpty(id))
      return 0;
    return DBManager.shared.tbCurStudyRecord.baseDeleteById(id!);
  }

  update(CurStudyRecord? curStudyRecord) async {
    return DBManager.shared.tbCurStudyRecord.baseUpdateById(curStudyRecord!.id!, curStudyRecord.toJson());
  }

  Future<CurStudyRecord?> queryCurStudyRecordById(String? id) async {
    if (StringUtil.isEmpty(id))
      return null;
    var queryRow = await DBManager.shared.tbCurStudyRecord.baseQueryById(id!);
    if (queryRow == null)
      return null;
    CurStudyRecord entity = CurStudyRecord.fromJson(Map<String, dynamic>.of(queryRow));

    return entity;
  }

  insert(CurStudyRecord? curStudyRecord) async {
    if (curStudyRecord == null)
      return;
    return DBManager.shared.tbCurStudyRecord.insert(curStudyRecord);
  }

  Future<CurStudyRecord?> queryByContendId(String? contendId) async {
    if (StringUtil.isEmpty(contendId))
      return null;
    var queryList = await DBManager.shared.tbCurStudyRecord.commQueryList(
        where: 'CONTENT_ID = ? ',
        whereArgs: [contendId],
        limit: 1
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    CurStudyRecord result = CurStudyRecord.fromJson(Map<String, dynamic>.of(queryList![0]));
    return result;

  }

  Future<List<CurStudyRecord>?> queryCurStudyRecordByIdList(List<String>? idList) async {
    if (CollectionUtil.isEmpty(idList))
      return null;
    var queryList = await DBManager.shared.tbCurStudyRecord.commQueryByIdList(idList!);
    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<CurStudyRecord> result = [];

    queryList?.forEach((element) {
      dynamic entity = CurStudyRecord.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  Future<void> insertList(List<CurStudyRecord> insertList) async {
    await DBManager.shared.tbCurStudyRecord.insertList(insertList);
  }

  deleteCurStudyRecordByContendId(String contentId) async {
    return await DBManager.shared.tbCurStudyRecord.baseDelete(
        where: 'CONTENT_ID = ? ',
        whereArgs: [contentId],
    );
  }
}