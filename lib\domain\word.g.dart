// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Word _$WordFromJson(Map<String, dynamic> json) => Word()
  ..id = json['ID'] as String?
  ..searchId = json['SEARCH_ID'] as String?
  ..ownerId = json['OWNER_ID'] as String?
  ..word = json['WORD'] as String?
  ..meaning = json['MEANING'] as String?
  ..userEdited = TypeSerialUtil.dynamicToBool(json['USER_EDITED'])
  ..category = json['CATEGORY'] as String?
  ..frequency = (json['FREQUENCY'] as num?)?.toInt();

Map<String, dynamic> _$WordToJson(Word instance) => <String, dynamic>{
      'ID': instance.id,
      'SEARCH_ID': instance.searchId,
      'OWNER_ID': instance.ownerId,
      'WORD': instance.word,
      'MEANING': instance.meaning,
      'USER_EDITED': TypeSerialUtil.boolToInt(instance.userEdited),
      'CATEGORY': instance.category,
      'FREQUENCY': instance.frequency,
    };
