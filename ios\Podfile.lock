PODS:
  - Flutter (1.0.0)
  - flutter_sound (9.27.0):
    - Flutter
    - flutter_sound_core (= 9.27.0)
  - flutter_sound_core (9.27.0)
  - flutter_tts (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - receive_sharing_intent (1.8.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - receive_sharing_intent (from `.symlinks/plugins/receive_sharing_intent/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - flutter_sound_core

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  receive_sharing_intent:
    :path: ".symlinks/plugins/receive_sharing_intent/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_sound: 47dd7c20f8acf17507ba504c76992198be7879a5
  flutter_sound_core: eed9770a54160cf4d4e1d3259c4c34738654df55
  flutter_tts: 0f492aab6accf87059b72354fcb4ba934304771d
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  receive_sharing_intent: 79c848f5b045674ad60b9fea3bafea59962ad2c1
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: ab172d1f43e44b51e73ab6963c76699511d4b45b

COCOAPODS: 1.15.2
