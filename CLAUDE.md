# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter language learning application called "words" that implements spaced repetition learning using the Ebbinghaus forgetting curve. The app helps users study vocabulary through an intelligent algorithm that optimizes review timing.

## Common Development Commands

### Build Commands
```bash
# Run the app in development mode
flutter run

# Build debug APK
flutter build apk --debug

# Build release APK
flutter build apk --release

# Build for iOS
flutter build ios
```

### Code Generation
```bash
# Generate localization files from ARB
flutter pub pub run intl_translation:extract_to_arb --output-dir=lib/l10n lib/l10n/loc_str.dart
flutter pub pub run intl_translation:generate_from_arb --output-dir=lib/l10n --no-use-deferred-loading lib/l10n/loc_str.dart lib/l10n/intl_zh.arb lib/l10n/intl_en.arb

# Generate JSON serialization code
flutter pub run build_runner build

# Build script (combines localization and code generation)
flutterbuild.bat
```

### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart
```

### Code Quality
```bash
# Analyze code for issues
flutter analyze

# Format code
flutter format .

# Get dependencies
flutter pub get
```

## Architecture Overview

### Layer Architecture
The application follows a clean architecture pattern with clear separation of concerns:

1. **UI Layer** (`lib/ui/`) - Presentation logic using BLoC pattern with Cubits
2. **Business Logic Layer** (`lib/biz/`) - Core business rules and algorithms
3. **Manager Layer** (`lib/manager/`) - Data access abstraction and orchestration
4. **Data Access Layer** (`lib/dao/`) - Database operations with SQLite
5. **Domain Layer** (`lib/domain/`) - Entity models and business objects

### Key Patterns

#### BLoC Pattern with Cubits
- **State Management**: Uses `flutter_bloc` with Cubits for reactive UI
- **Key Cubits**: `HomeCubit` (study logic), `BookContentCubit` (book operations)
- **State Classes**: Immutable state objects with cloning for modifications
- **Unidirectional Flow**: UI → Cubit → State → UI updates

#### Database Architecture
- **Database**: SQLite with `sqflite` package
- **DAO Pattern**: Table-specific data access objects extending `DBBaseProvider`
- **Manager Pattern**: Higher-level abstraction over DAOs for business operations
- **Asset Deployment**: Initial database bundled as asset (`assets/resources/repeat_mem.db`)

#### Code Generation
- **JSON Serialization**: Domain models use `json_serializable` with `@JsonSerializable()` annotations
- **Generated Files**: `.g.dart` files contain serialization/deserialization logic
- **Build Runner**: `flutter pub run build_runner build` generates code

#### Localization System
- **ARB Files**: Translation files in `lib/l10n/` (`intl_en.arb`, `intl_zh.arb`)
- **Generated Classes**: Type-safe localization classes from ARB files
- **Runtime Switching**: Support for English and Chinese with dynamic locale switching

### Core Modules

#### Study System
- **Spaced Repetition**: Implements Ebbinghaus forgetting curve algorithm
- **Progress Tracking**: Records study sessions and calculates review intervals
- **Word Relations**: Supports associated words and contextual learning

#### Book Management
- **Content Structure**: Hierarchical organization of books and content
- **Study Tracking**: Records progress through book content
- **Bookmarking**: User-defined bookmarks for important sections

#### Dictionary Integration
- **Word Lookup**: Dictionary functionality with pronunciation
- **Word Addition**: Add new words from external sources
- **TTS Integration**: Text-to-speech for word pronunciation

#### System Configuration
- **User Management**: Multi-user support with system and regular users
- **Properties**: Configurable system and user properties
- **Path Management**: Platform-specific file paths for iOS and Android

## Database Schema

The SQLite database contains these key tables:
- `tb_words` - Word definitions and metadata
- `tb_book` - Book information
- `tb_book_content` - Book content hierarchy
- `tb_study_record` - Study session records
- `tb_ebbinghause_point` - Spaced repetition algorithm data
- `tb_word_relation` - Word associations
- `tb_system_properties` - System configuration

## Development Notes

### Platform-Specific Code
- **Android**: Uses predefined paths for database and storage
- **iOS**: Creates paths dynamically using `getDatabasesPath()`
- **Permissions**: Handles storage and microphone permissions for TTS

### Dependencies Management
- **State Management**: `flutter_bloc` for BLoC pattern
- **Database**: `sqflite` for SQLite operations
- **Serialization**: `json_annotation` and `json_serializable`
- **Localization**: `intl` and `intl_translation`
- **UI**: `cupertino_icons`, `easy_refresh`, `scrollable_positioned_list`
- **Audio**: `flutter_tts`, `flutter_sound`
- **Sharing**: `share_plus`, `receive_sharing_intent`

### File Structure Conventions
- **Domain Models**: In `lib/domain/` with corresponding `.g.dart` files
- **Data Access**: DAO classes in `lib/dao/` with base provider in `support/`
- **Business Logic**: Singleton services in `lib/biz/`
- **UI Components**: Pages in `lib/ui/` organized by feature
- **Utilities**: Helper functions in `lib/commonSupport/utils/` and `lib/utils/`

### Generated Code
- Always run `flutterbuild.bat` after modifying:
  - Domain models (for JSON serialization)
  - Localization strings (for ARB generation)
- Generated files should not be manually edited
- Check generated files into version control