import 'dart:io';
import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/dao/tb_book.dart';
import 'package:words/dao/tb_book_content.dart';
import 'package:words/dao/tb_book_mark.dart';
import 'package:words/dao/tb_cur_study_record.dart';
import 'package:words/dao/tb_ebbinghause_point.dart';
import 'package:words/dao/tb_statistics_level_sum.dart';
import 'package:words/dao/tb_study_record.dart';
import 'package:words/dao/tb_system_properties.dart';
import 'package:words/dao/tb_word_history.dart';
import 'package:words/dao/tb_word_relation.dart';
import 'package:words/dao/tb_words.dart';


class DBManager {
  // 单例
  factory DBManager() => _shared();
  static DBManager get shared => _shared();
  static DBManager? _instance;
  DBManager._();
  static DBManager _shared() {
    if (_instance == null) {
      _instance = DBManager._();
    }
    return _instance!;
  }

  static const int _VERSION = 1;
  Database? _database;

  final TbWord tbWord = TbWord();
  final TbBook tbBook = TbBook();
  final TbStudyRecord tbStudyRecord = TbStudyRecord();
  final TbWordRelation tbWordRelation = TbWordRelation();
  final TbWordHistory tbWordHistory = TbWordHistory();
  final TbEbbinghausPoint tbEbbinghausPoint = TbEbbinghausPoint();
  final TbSystemProperty tbSystemProperty = TbSystemProperty();
  final TbCurStudyRecord tbCurStudyRecord = TbCurStudyRecord();
  final TbStatisticsLevelSum tbStatisticsLevelSum = TbStatisticsLevelSum();
  final TbBookContent tbBookContent = TbBookContent();
  final TbBookMark tbBookMark = TbBookMark();

  init() async {
    if (_database == null) {
      String dbFilePath = SystemConfig.DB_BASE_PATH + Platform.pathSeparator + SystemConfig.DB_NAME;

      print("------数据库地址: $dbFilePath");
      bool isExists = await databaseExists(dbFilePath);

      if (!isExists) {
        ByteData data = await rootBundle.load("assets/resources/" + SystemConfig.DB_NAME);
        List<int> bytes = data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

        var directory = Directory(SystemConfig.DB_BASE_PATH).createSync(recursive: true);
        File(dbFilePath).writeAsBytesSync(bytes, flush: true);
      }

      // 数据库初始化，如果没有数据库则创建
      try{
        await Sqflite.devSetDebugModeOn(SystemConfig.DEBUG_DB);
        await openDb(dbFilePath);
      }catch (err) {
        throw(err);
      }
      Batch? batch = _database?.batch();
      _createTable().forEach((element) {
        batch?.execute(element);
      });
      await batch?.commit();
    }
  }

  openDb(String dbFilePath) async {
    if (_database!= null && _database!.isOpen)
      return;
    _database = await openDatabase(dbFilePath, version: _VERSION,
      onCreate: (Database db, int version) async
      {
        try {
          Batch batch = db.batch();
          _createTable().forEach((element) {
            batch.execute(element);
          });
          batch.commit();
        }catch (err) {
          throw(err);
        }
      },
      onUpgrade: (Database db, int oldVersion, int newVersion) {
        try{
          Batch batch = db.batch();
          _createTable().forEach((element) {
            batch.execute(element);
          });
          batch.commit();
        }catch (err) {
          throw(err);
        }
      },
    );
  }
  // 关闭数据库
  closeDb() async {
    if (_database!= null && _database!.isOpen)
      await _database!.close();
  }

  Future<Database?> getDb() async {
    if (_database == null)
      await init();
    return _database;
  }

  List<String> _createTable(){
    return [
      tbWord.tableCreateSql(),
      tbBook.tableCreateSql(),
      tbStudyRecord.tableCreateSql(),
      tbWordRelation.tableCreateSql(),
      tbWordHistory.tableCreateSql()
    ];
  }

  Future<void> runTransaction(Future<void> Function(Transaction txn) action) async {
    await _database?.transaction(action);
  }

  static Future<bool> verifyDb(String? dbFilename) async {
    if (StringUtil.isEmpty(dbFilename))
      return false;

    if (!await databaseExists(dbFilename!)) {
      return false;
    }

    Database db = await openDatabase(dbFilename);

    // Check if the 'my_table' table exists
    List<Map> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='EBBINGHAUS_POINT'");
    if (tables.length != 1) {
      await db.close();
      return false;
    }

    await db.close();
    return true;
  }
}