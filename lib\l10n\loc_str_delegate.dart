import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:words/l10n/loc_str.dart';

class LocStrDelegate  extends LocalizationsDelegate<LocStr> {

  @override
  Future<LocStr> load(Locale locale) {
    return LocStr.load(locale);
  }

  @override
  bool isSupported(Locale locale) => ['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(LocStrDelegate old) => false;

}
