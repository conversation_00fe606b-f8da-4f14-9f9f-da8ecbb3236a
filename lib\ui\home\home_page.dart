import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:words/common/system_config.dart';
import 'package:words/common/system_const.dart';
import 'package:words/commonSupport/utils/type_serial_util.dart';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/home/<USER>';

class HomePage extends StatefulWidget {
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {

  @override
  void initState() {
    super.initState();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create: (BuildContext bcontext) => HomeCubit()..studyNextWord(context)
          ..silentMode = TypeSerialUtil.stringToBoolStrict(SystemConfig.getUserProperty(SystemConst.P_DEFAULT_SILENT_MODE)),
        child: BlocBuilder<HomeCubit, HomeState>(
          builder: (ctx, state) {
            return Container(
              color: Colors.white,
              height: MediaQuery.of(context).size.height,
              child: Column(
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).padding.top,
                    color: Colors.blue,
                  ),
                  HomeFirstView(BlocProvider.of<HomeCubit>(ctx)),
                  Expanded(
                    child: HomeSecondView(BlocProvider.of<HomeCubit>(ctx)),
                  ),
                  HomeFourView((index) async {
                    switch (index) {
                      case 0:
                        await BlocProvider.of<HomeCubit>(ctx).remberedBtn(ctx);
                        break;
                      case 1:
                        await BlocProvider.of<HomeCubit>(ctx).blurryBtn(ctx);
                        break;
                      case 2:
                        await BlocProvider.of<HomeCubit>(ctx).forgetBtn(ctx);
                        break;
                      }
                    }
                  ),
                ],
              ),
            );
          }
        )
      )
    );
  }
}

