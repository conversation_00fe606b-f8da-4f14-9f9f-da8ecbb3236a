import 'package:words/common/system_config.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/cur_study_record.dart';
import 'package:words/domain/statistics_count_dto.dart';
import 'package:words/domain/study_record.dart';
import 'package:words/commonSupport/utils/date_util.dart';

import 'package:words/common/system_const.dart';
import 'package:words/domain/word.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';

class StudyRecordManager {
  //单例模式
  factory StudyRecordManager() => _shared();
  static StudyRecordManager get shared => _shared();
  static StudyRecordManager? _instance;
  StudyRecordManager._();
  static StudyRecordManager _shared() {
    if (_instance == null) {
      _instance = StudyRecordManager._();
    }
    return _instance!;
  }

  Future<int?> calRemainExpiredCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbStudyRecord.commCount(
      where: 'USER_ID = ? AND ORI_STUDY_LEVEL > 0 AND ORI_NEXT_STUDY_DATE <= ?',
      whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch]
    );
    return res;
  }

  calRemainNewCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL = 0',
        whereArgs: [SystemConfig.user!.id]
    );
    return res;
  }

  calRemainNewStartedCurStudyRcd(DateTime now) async {
    var res = await DBManager.shared.tbStudyRecord.commCount(
        where: 'USER_ID = ? AND ORI_STUDY_LEVEL = 0 AND CUR_STUDY_LEVEL != 0',
        whereArgs: [SystemConfig.user!.id]
    );
    return res;
  }

  calRemainTodayCurStudyRcd(DateTime now) async {
    DateTime todayBorder = DateUtil.getTodayBoundary();
    var res = await DBManager.shared.tbStudyRecord.commCount(
        where: 'USER_ID = ? '
            'AND ORI_STUDY_LEVEL > 0 '
            'AND ORI_MIN_NEXT_STUDY_DATE <= ? '
            'AND ORI_NEXT_STUDY_DATE > ? '
            'AND ORI_NEXT_STUDY_DATE < ?',
        whereArgs: [SystemConfig.user!.id, now.millisecondsSinceEpoch, now.millisecondsSinceEpoch, todayBorder.millisecondsSinceEpoch]
    );
    return res;
  }

  calRemainCommingCurStudyRcd(DateTime now) async{
    int nowMills = now.millisecondsSinceEpoch;
    var res = await DBManager.shared.tbStudyRecord.commCount(
        where: 'USER_ID = ? '
            'AND ORI_STUDY_LEVEL > 0 '
            'AND ORI_MIN_NEXT_STUDY_DATE <= ? '
            'AND ORI_NEXT_STUDY_DATE > ?',
        whereArgs: [SystemConfig.user!.id, nowMills, nowMills]
    );
    return res;
  }

  Future<List<StudyRecord>?> getStartedNotExpiredRecord(String id, DateTime now) async {
    int nowMills = now.millisecondsSinceEpoch;
    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: 'USER_ID = ? '
            'AND STUDY_LEVEL != ${SystemConst.TOP_REPEAT_LEV} '
            'AND NEXT_STUDY_DATE > ?',
        whereArgs: [SystemConfig.user!.id, nowMills],
        orderBy: 'NEXT_STUDY_DATE ASC',
        limit: 1
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<StudyRecord> result = [];
    queryList?.forEach((element) {
      dynamic entity = StudyRecord.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  deleteStudyRecordByContentId(String contentId) async {
    if (StringUtil.isEmpty(contentId))
      return;
    return await DBManager.shared.tbStudyRecord.baseDelete(
      where: 'CONTENT_ID = ? ',
      whereArgs: [contentId],
    );
  }

  Future<StudyRecord?> queryById(String? id) async {
    if (StringUtil.isEmpty(id))
      return null;
    var queryRow = await DBManager.shared.tbStudyRecord.baseQueryById(id!);
    if (queryRow == null)
      return null;
    StudyRecord entity = StudyRecord.fromJson(Map<String, dynamic>.of(queryRow));

    return entity;
  }

  Future<int?> update(StudyRecord? studyRecord) async {
    if (studyRecord == null)
      return null;
    return await DBManager.shared.tbStudyRecord.baseUpdateById(studyRecord.id!, studyRecord.toJson());
  }

  Future<StudyRecord?> queryByContendId(String? contendId) async {
    if (StringUtil.isEmpty(contendId))
      return null;
    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: 'CONTENT_ID = ? ',
        whereArgs: [contendId],
        limit: 1
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    StudyRecord result = StudyRecord.fromJson(Map<String, dynamic>.of(queryList![0]));
    return result;

  }

  Future<List<StudyRecord>?> calStudyRecordByPage(String userId, DateTime time, int currentStart, int pageSize) async {
    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: 'USER_ID = ? '
            'AND (NEXT_STUDY_DATE <= ? OR MIN_NEXT_STUDY_DATE <= ?) ',
        whereArgs: [SystemConfig.user!.id, time.millisecondsSinceEpoch, time.millisecondsSinceEpoch],
        orderBy: 'NEXT_STUDY_DATE ASC, STUDY_LEVEL ASC, MIN_NEXT_STUDY_DATE ASC ',
        offset: currentStart,
        limit: pageSize
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<StudyRecord> result = [];
    queryList?.forEach((element) {
      dynamic entity = StudyRecord.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  Future<List<StudyRecord>?> queryByContentSearchIdList(List<String>? searchIds) async {
    if (CollectionUtil.isEmpty(searchIds))
      return null;
    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: 'CONTENT_SEARCH_ID IN (${StringUtil.toSqlWhereString(searchIds!)})'
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<StudyRecord> result = [];
    queryList?.forEach((element) {
      dynamic entity = StudyRecord.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  Future<List<StudyRecord>?> queryByContendIdList(List<String>? contentIds) async {
    if (CollectionUtil.isEmpty(contentIds))
      return null;

    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: 'CONTENT_ID IN (${StringUtil.toSqlWhereString(contentIds!)})'
    );
    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<StudyRecord> result = [];
    queryList?.forEach((element) {
      dynamic entity = StudyRecord.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  Future<bool> wordStudyRecordExisted(String? wordId) async {
    return await DBManager.shared.tbStudyRecord.wordStudyRecordExisted(wordId!);
  }

  Future <int> addStrangeWordToRecord(Word? word) async {
    if (word == null)
      return await 0;
    StudyRecord studyRecord = new StudyRecord.fromWord(word);
    studyRecord.studyLevel = 0;

    DateTime now = DateTime.now();
    studyRecord.addDate = now;
    //初次添加词，不能用Ebbinghaus计算，直接初始化为现在，表示现在开始
    studyRecord.nextStudyDate = now;
    studyRecord.minNextStudyDate = now;
    studyRecord.addDate = now;

    studyRecord.neverSeen = true;
    return await addStudyRecordExclusively(studyRecord);
  }

  Future<int> addStudyRecordExclusively(StudyRecord studyRecord) async {
    var queryList = await DBManager.shared.tbStudyRecord.commQueryList(
        where: ' USER_ID = ? '
            ' AND CONTENT_ID = ? ',
        whereArgs: [studyRecord.userId, studyRecord.contentId]
    );

    if (CollectionUtil.nonEmpty(queryList))
      return 0;

    await DBManager.shared.tbStudyRecord.insert(studyRecord);
    return 1;
  }

  Future<int> deleteStudyRecord(StudyRecord studyRecord) async {
    if (StringUtil.isEmpty(studyRecord.id))
      return 0;
    return await DBManager.shared.tbStudyRecord.baseDeleteById(studyRecord.id!);
  }

  Future<List<StatisticsCountDto>?> queryCommingCountByPage(String userId, int? begin, int? pageSize) async {
    String sql = '''
      SELECT 
        STRFTIME('%Y-%m-%d', next_study_date/1000 + 28800 - 14400, 'unixepoch') || '  ' ||
        CASE CAST(STRFTIME('%w', next_study_date/1000 + 28800 - 14400, 'unixepoch') AS INTEGER)
          WHEN 0 THEN 'Sun'
          WHEN 1 THEN 'Mon'
          WHEN 2 THEN 'Tue'
          WHEN 3 THEN 'Wed'
          WHEN 4 THEN 'Thu'
          WHEN 5 THEN 'Fri'
          WHEN 6 THEN 'Sat'
        END as DATE,
        COUNT(*) as COUNT 
      FROM STUDY_RECORD 
      WHERE USER_ID = '${userId}'
      GROUP BY DATE 
      ORDER BY DATE ASC 
      LIMIT ${pageSize} OFFSET ${begin}
    ''';
    List<Map<String, Object?>>? queryRes = await DBManager.shared.tbStudyRecord.baseRawQueryList(sql);
    if (CollectionUtil.isEmpty(queryRes))
      return null;
    List<StatisticsCountDto> result = [];
    queryRes?.forEach((element) {
      dynamic entity = StatisticsCountDto.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }


}