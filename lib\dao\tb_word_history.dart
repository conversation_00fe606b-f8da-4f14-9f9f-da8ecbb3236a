import 'package:sqflite/sqflite.dart';
import 'package:words/domain/word.dart';
import 'support/db_base_provider.dart';

class TbWordHistory extends DBBaseProvider {

  @override
  String get tableName => "WORD_HISTORY";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID text not null,
        SEARCH_ID text,
        OWNER_ID text,
        WORD text,
        MEANING text,
        FREQUENCY int,
        CATEGORY text,
        USER_EDITED int,
        HISTORY_INDEX int AUTO_INCREMENT
        )
        ''';
  }

  queryList() async {
    final Database? db = await dataBase;
    final List<Map<String, Object?>>? maps = await db?.query(tableName, orderBy: "HISTORY_INDEX");
    List<Word> _result = [];
    if (maps != null) {
      maps.forEach((element) {
        Word word = Word.fromJson(element);
        _result.add(word);
      });
    }
    return _result;
  }

  insertWord(Word entity) async {
    Map<String, Object> map = {
      "ID": entity.id!,
      "SEARCH_ID": entity.searchId!,
      "OWNER_ID": entity.ownerId!,
      "WORD": entity.word!,
      "MEANING": entity.meaning!,
      "FREQUENCY": entity.frequency ?? 0,
      "CATEGORY": entity.category ?? "",
      "USER_EDITED": entity.userEdited ?? ""
    };
    return await baseInsert(map);
  }

  deleteWord(Word entity) async {
    return await baseDeleteById(entity.id!);
  }

}