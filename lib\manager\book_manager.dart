import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/book_content.dart';

import 'package:words/domain/word.dart';

class BookManager {
  //单例模式
  factory BookManager() => _shared();
  static BookManager get shared => _shared();
  static BookManager? _instance;
  BookManager._();
  static BookManager _shared() {
    if (_instance == null) {
      _instance = BookManager._();
    }
    return _instance!;
  }



  

}