import 'package:sqflite/sqflite.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/ebbinghaus_point.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class TbEbbinghausPoint extends DBBaseProvider {

  @override
  String get tableName => "EBBINGHAUS_POINT";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        USER_ID TEXT,
        POINT_TYPE_STR TEXT,
        POINTS_STR TEXT,
        CONSTRAINT "EBBINGHAUS_POINT_PK" PRIMARY KEY ("ID")
        )
        ''';
  }

  getEbbinghausPoint(String userId, EbbinghausPointType type) async {
    List<Map<String, Object?>>? queryList = await commQueryList(
      where: "USER_ID = ? AND POINT_TYPE_STR = ?",
      whereArgs: [userId, type.value]);

    List<EbbinghausPoint> result = [];
    if (CollectionUtil.isEmpty(queryList)) return null;

    queryList?.forEach((element) {
      EbbinghausPoint entity = EbbinghausPoint.fromJson(Map<String, dynamic>.of(element));
      entity.buildPropertyFromString();
      result.add(entity);
    });

    return result[0];
  }

}