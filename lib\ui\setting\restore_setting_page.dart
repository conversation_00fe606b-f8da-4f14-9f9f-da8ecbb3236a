import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/toast_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/main.dart';

class RestoreSettingPage extends StatefulWidget {
  final List<File>? files;

  RestoreSettingPage({this.files}) {}

  _RestoreSettingPageState createState() => _RestoreSettingPageState();
}

class _RestoreSettingPageState extends State<RestoreSettingPage> {
  final TextEditingController _confirmController = TextEditingController();

  Timer? _timer;
  bool _isBackingUp = false;
  double _progress = 0.0; // Progress bar value

  bool success = false;
  bool finished = false;
  String resultStr = "";

  @override
  void dispose() {
    if (_timer != null) _timer?.cancel();
    _confirmController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final MyApp? app = context.findAncestorWidgetOfExactType<MyApp>();
    return Scaffold(
      appBar: AppBar(
        title: Text('Restore'),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: _RestorePage(app),
      ),
    );
  }

  _RestorePage(MyApp? app) {
    if (finished == false) {
      return SingleChildScrollView(
        child: Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15), // Adjust this for your desired corner radius
          ),
          elevation: 5, // Adjust this for your desired shadow depth
          child: Padding(
            padding: EdgeInsets.all(16.0), // Add padding inside the card
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _isBackingUp
                    ? LinearProgressIndicator(value: _progress)
                    : Container(),
                SizedBox(height: 20),
                Text(
                    LocStr.of(context)!.restoreWarning!,
                  style: TextStyle(
                      fontSize: 18.0, // change this value for different font sizes
                      color: Colors.redAccent[400]),
                ),
                SizedBox(height: 10),
                GestureDetector(
                  onTap: () async {
                    await WordBiz.shared.shareBackupDb(context);
                  },
                  child: Text(
                    LocStr.of(context)!.clickToBackup!,
                    style: TextStyle(
                        fontSize: 18.0,
                        // change this value for different font sizes
                        color: Colors.deepPurple),
                  ),
                ),
                SizedBox(height: 20),
                TextFormField(
                  controller: _confirmController,
                  decoration: InputDecoration(
                    labelText: LocStr.of(context)!.inputToConfirmRestore!,
                    labelStyle: TextStyle(
                        fontSize: 16.0,
                        // change this value for different font sizes
                        color: Colors.deepPurple),
                  ),
                ),
                SizedBox(height: 30),
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      startRestore();
                    },
                    child: Text(LocStr.of(context)!.restoreData),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              resultStr,
              style: TextStyle(
                fontSize: 20.0,
                // change this value for different font sizes
                color: success ? Colors.black54 : Colors.red,
              ),
            ),
            SizedBox(
              height: 30,
            ),
            success ? ElevatedButton(
                    onPressed: () {
                      app?.state.restartApp();
                    },
                    child: Text(LocStr.of(context)!.restartNow),
                  )
                : Container(),
          ]
        )
      );
    }
  }

  Future<void> startRestore() async {
    if (_confirmController.text != "restore" ||
        widget.files == null ||
        widget.files!.length == 0) {
      _confirmController.clear();
      ToastUtil.showToast(LocStr.of(context)!.incorrectInput!);
      return;
    }
    _confirmController.clear();

    if (widget.files == null || widget.files!.length == 0) {
      setState(() {
        resultStr = LocStr.of(context)!.restoreFailNoFile!;
        finished = true;
      });
      return;
    }

    var fileIsValid = await DBManager.verifyDb(widget.files![0].path!);
    if (!fileIsValid) {
      setState(() {
        resultStr = LocStr.of(context)!.restoreFailInvalidFile!;
        finished = true;
      });
      return;
    }

    setState(() {
      _isBackingUp = true;
      _progress = 0.0;
    });
    const oneSec = const Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (Timer timer) {
      setState(() {
        if ((_progress >= 1) || (_isBackingUp == false)) {
          timer.cancel();
          _isBackingUp = false;
          _progress = 0.0;
          // Handle completion of Restore process
        } else {
          _progress += 0.2; // Increment the progress value
        }
      });
    });

    await DBManager.shared.closeDb();

    try {
      await widget.files![0].copy(SystemConfig.FULL_DB_PATH);
      setState(() {
        resultStr = LocStr.of(context)!.restoreSuccess!;
        success = true;
        finished = true;
      });
    } catch (e) {
      setState(() {
        resultStr = LocStr.of(context)!.restoreFailCopyError!;
        finished = true;
      });
    }
    _isBackingUp = false;

    DBManager.shared.openDb(SystemConfig.FULL_DB_PATH);
  }
}
