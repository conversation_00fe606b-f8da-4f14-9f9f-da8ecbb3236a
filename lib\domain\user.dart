import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'user.g.dart';

@JsonSerializable()
class User {
	@Json<PERSON>ey(name: "ID")
	late String id;

	@Json<PERSON>ey(name: "LOGIN_NAME")
	late String loginName;

	@<PERSON><PERSON><PERSON>ey(name: "NICK_NAME")
	late String nickName;

	User();
	User.namedConstructor(this.id, this.loginName, this.nickName);

	factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

	Map<String, dynamic> toJson() => _$UserToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}