// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_level_sum.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StatisticsLevelSum _$StatisticsLevelSumFromJson(Map<String, dynamic> json) =>
    StatisticsLevelSum()
      ..id = json['ID'] as String?
      ..userId = json['USER_ID'] as String?
      ..studyLevel = (json['STUDY_LEVEL'] as num?)?.toInt()
      ..durationStr = json['DURATION_STR'] as String?
      ..remberSum = (json['REMBER_SUM'] as num?)?.toInt()
      ..forgetSum = (json['FORGET_SUM'] as num?)?.toInt()
      ..blurrySum = (json['BLURRY_SUM'] as num?)?.toInt()
      ..lastUpdateTime =
          TypeSerialUtil.dynamicToDateTime(json['LAST_UPDATE_TIME'])
      ..pointChgTime = TypeSerialUtil.dynamicToDateTime(json['POINT_CHG_TIME'])
      ..neverSeen = TypeSerialUtil.dynamicToBool(json['NEVER_SEEN'])
      ..startStatTime =
          TypeSerialUtil.dynamicToDateTime(json['START_STAT_TIME']);

Map<String, dynamic> _$StatisticsLevelSumToJson(StatisticsLevelSum instance) =>
    <String, dynamic>{
      'ID': instance.id,
      'USER_ID': instance.userId,
      'STUDY_LEVEL': instance.studyLevel,
      'DURATION_STR': instance.durationStr,
      'REMBER_SUM': instance.remberSum,
      'FORGET_SUM': instance.forgetSum,
      'BLURRY_SUM': instance.blurrySum,
      'LAST_UPDATE_TIME': TypeSerialUtil.dateTimeToInt(instance.lastUpdateTime),
      'POINT_CHG_TIME': TypeSerialUtil.dateTimeToInt(instance.pointChgTime),
      'NEVER_SEEN': TypeSerialUtil.boolToInt(instance.neverSeen),
      'START_STAT_TIME': TypeSerialUtil.dateTimeToInt(instance.startStatTime),
    };
