import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';
part 'book_mark.g.dart';

@JsonSerializable()
class BookMark {
	@Json<PERSON>ey(name: "ID")
	String? id;

	@<PERSON><PERSON><PERSON><PERSON>(name: "BOOK_ID")
	String? bookId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "USER_ID")
	String? userId;

	@<PERSON>son<PERSON><PERSON>(name: "CUR_SEQUENCE")
	int? curSequence;

	BookMark();

	factory BookMark.fromJson(Map<String, dynamic> json) => _$BookMarkFromJson(json);

	Map<String, dynamic> toJson() => _$BookMarkToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}