import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/book_content.dart';

import 'package:words/domain/word.dart';

class BookContentManager {
  //单例模式
  factory BookContentManager() => _shared();
  static BookContentManager get shared => _shared();
  static BookContentManager? _instance;
  BookContentManager._();
  static BookContentManager _shared() {
    if (_instance == null) {
      _instance = BookContentManager._();
    }
    return _instance!;
  }

  Future<List<BookContent>?> searchBookContentByPage(String? bookId, int? begin, int? pageSize) async {
    if (StringUtil.isEmpty(bookId))
      return null;
    var queryList = await DBManager.shared.tbBookContent.commQueryList(
        where: 'BOOK_ID = ? ',
        whereArgs: [bookId],
        orderBy: 'SEQUENCE ASC',
        offset: begin,
        limit: pageSize
    );

    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<BookContent>? result = [];
    queryList?.forEach((element) {
      BookContent entity = BookContent.fromJson(
          Map<String, dynamic>.of(element));
      result.add(entity);
    });

    return result;

  }

  

}