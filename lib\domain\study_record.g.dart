// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'study_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudyRecord _$StudyRecordFromJson(Map<String, dynamic> json) => StudyRecord()
  ..id = json['ID'] as String?
  ..contentSearchId = json['CONTENT_SEARCH_ID'] as String?
  ..userId = json['USER_ID'] as String?
  ..studyLevel = (json['STUDY_LEVEL'] as num?)?.toInt()
  ..firstStudyDate = TypeSerialUtil.dynamicToDateTime(json['FIRST_STUDY_DATE'])
  ..lastStudyDate = TypeSerialUtil.dynamicToDateTime(json['LAST_STUDY_DATE'])
  ..minNextStudyDate =
      TypeSerialUtil.dynamicToDateTime(json['MIN_NEXT_STUDY_DATE'])
  ..nextStudyDate = TypeSerialUtil.dynamicToDateTime(json['NEXT_STUDY_DATE'])
  ..neverSeen = TypeSerialUtil.dynamicToBool(json['NEVER_SEEN'])
  ..addDate = TypeSerialUtil.dynamicToDateTime(json['ADD_DATE'])
  ..curEbbinghausStr = json['CUR_EBBINGHAUS_STR'] as String?
  ..contentId = json['CONTENT_ID'] as String?;

Map<String, dynamic> _$StudyRecordToJson(StudyRecord instance) =>
    <String, dynamic>{
      'ID': instance.id,
      'CONTENT_SEARCH_ID': instance.contentSearchId,
      'USER_ID': instance.userId,
      'STUDY_LEVEL': instance.studyLevel,
      'FIRST_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.firstStudyDate),
      'LAST_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.lastStudyDate),
      'MIN_NEXT_STUDY_DATE':
          TypeSerialUtil.dateTimeToInt(instance.minNextStudyDate),
      'NEXT_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.nextStudyDate),
      'NEVER_SEEN': TypeSerialUtil.boolToInt(instance.neverSeen),
      'ADD_DATE': TypeSerialUtil.dateTimeToInt(instance.addDate),
      'CUR_EBBINGHAUS_STR': instance.curEbbinghausStr,
      'CONTENT_ID': instance.contentId,
    };
