import 'dart:io';
import 'package:flutter_tts/flutter_tts.dart';

class TtsUtils {

  static ttsSetup() async {
    FlutterTts flutterTts = FlutterTts();
    if (Platform.isIOS) {
      await flutterTts.setSharedInstance(true);
      // 允许多声到，例如音乐播放的同事，app中朗读也可以同时进行
      await flutterTts.setIosAudioCategory(IosTextToSpeechAudioCategory.ambient,
          [
            IosTextToSpeechAudioCategoryOptions.allowBluetooth,
            IosTextToSpeechAudioCategoryOptions.allowBluetoothA2DP,
            IosTextToSpeechAudioCategoryOptions.mixWithOthers
          ],
          IosTextToSpeechAudioMode.voicePrompt
      );
    }
    // 等待朗读结束
    await flutterTts.awaitSpeakCompletion(true);
    // 朗读的英文
    await flutterTts.setLanguage("en-US");
    // 语速0~1
    await flutterTts.setSpeechRate(0.5);
    // 音量
    await flutterTts.setVolume(1.0);
    // await flutterTts.setPitch(1.0);
    // 只允许英文
    await flutterTts.isLanguageAvailable("en-US");

    // await flutterTts.setVoice({"name": "Karen", "locale": "en-AU"});
  }

  // 获取支持朗读的语言
  static getLanguage() async {
    FlutterTts flutterTts = FlutterTts();
    List<dynamic> languages = await flutterTts.getLanguages;
    return languages;
  }

  static getSetting() async {
    FlutterTts flutterTts = FlutterTts();
    await flutterTts.getEngines;
    await flutterTts.getDefaultVoice;
    await flutterTts.isLanguageInstalled("en-AU");
    await flutterTts.areLanguagesInstalled(["en-AU", "en-US"]);
    // await flutterTts.setQueueMode(1);
    await flutterTts.getMaxSpeechInputLength;

  }

  static speak(String word) async {
    FlutterTts flutterTts = FlutterTts();
    return await flutterTts.speak(word);
  }

  static stop() async {
    FlutterTts flutterTts = FlutterTts();
    return await flutterTts.stop();
  }

  static pause() async {
    FlutterTts flutterTts = FlutterTts();
    return await flutterTts.pause();
  }
}