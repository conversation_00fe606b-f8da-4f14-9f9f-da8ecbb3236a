<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        /* Style the tab */
        .tab {
            position: sticky;
            top: 0;
            display: flex;
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            margin-top: 0;  /* Add this line */
            padding-top: 0;  /* Add this line */
        }

        /* Style the buttons inside the tab */
        .tab button {
            flex: 1;  /* Add this line */
            background-color: inherit;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            font-size: 34px;
        }
        /* Change background color of buttons on hover */
        .tab button:hover {
            background-color: #ddd;
        }

        /* Create an active/current tablink class */
        .tab button.active {
            background-color: #ccc;
        }

        /* Style the tab content */
        .tabcontent {
            display: none;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-top: none;
        }
        p {
            font-size: 20px;
        }
        body {
            font-size: 20px;
            margin-top: 0px;
            margin-right: 8px;
            margin-bottom: 8px;
            margin-left: 8px;
        }
        .centered-image {
            width: 70%;  /* 设置图片宽度为50% */
            display: block;  /* 将图片显示为块级元素，这样才能应用margin自动居中 */
            margin-left: auto;  /* 左边距自动 */
            margin-right: auto;  /* 右边距自动 */
        }
    </style>
</head>
<body>

<div class="tab">
    <button class="tablinks" onclick="openTab(event, 'Tab1')">学习</button>
    <button class="tablinks" onclick="openTab(event, 'Tab2')">词典</button>
    <button class="tablinks" onclick="openTab(event, 'Tab3')">课本</button>
    <button class="tablinks" onclick="openTab(event, 'Tab4')">设置</button>
</div>

<div id="Tab1" class="tabcontent">
    <img class="centered-image" src="images/study_1.jpg" alt="study_1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <h3>Tab1</h3>
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
    <p>Tab1 content.</p>
    <img src="image1.jpg" alt="Image1">
</div>

<div id="Tab2" class="tabcontent">
    <h3>Tab2</h3>
    <p>Tab2 content.</p>
    <img src="image2.jpg" alt="Image2">
</div>

<div id="Tab3" class="tabcontent">
    <h3>Tab3</h3>
    <p>Tab3 content.</p>
    <img src="image3.jpg" alt="Image3">
</div>

<div id="Tab4" class="tabcontent">
    <h3>Tab4</h3>
    <p>Tab4 content.</p>
    <img src="image4.jpg" alt="Image4">
</div>

<script>
function openTab(evt, tabName) {
  var i, tabcontent, tablinks;
  tabcontent = document.getElementsByClassName("tabcontent");
  for (i = 0; i < tabcontent.length; i++) {
    tabcontent[i].style.display = "none";
  }
  tablinks = document.getElementsByClassName("tablinks");
  for (i = 0; i < tablinks.length; i++) {
    tablinks[i].className = tablinks[i].className.replace(" active", "");
  }
  document.getElementById(tabName).style.display = "block";
  evt.currentTarget.className += " active";
}

// Get the element with id="defaultOpen" and click on it
window.onload = function() {
  var firstTab = document.getElementsByClassName("tablinks")[0];
  openTab({currentTarget: firstTab}, 'Tab1');
};


</script>
</body>
</html>