import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:words/biz/word_biz.dart';
import 'package:words/common/recyclerList/recycler_list_data_handler.dart';
import 'package:words/common/recyclerList/word_content_querier.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/media_utils.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/widget/single_word_dialog/common_single_word_btn_callback.dart';
import 'package:words/utils/tts/tts_utils.dart';
import 'package:words/domain/word.dart';
import 'package:words/ui/book/book_content_cubit.dart';
import 'dart:io';

import 'add_word_to_dict_dialog.dart';
import 'single_word_dialog/single_word_dialog.dart';

class ComAddWordRelDialog extends StatefulWidget {
  Word? curWord;
  String? searchString;
  String? searchString2;
  String? initString;
  RecycleListDataHandler? recycleListDataHandler;
  Function? addHandler;
  bool isSearching = false;
  bool searchWaiting = false;

  ComAddWordRelDialog(Word curWord, String? initString,Function addHandler) {
    this.curWord = curWord;
    this.addHandler = addHandler;
    this.initString = initString;
    WordContentQuerier wordContentQuerier = WordContentQuerier();
    recycleListDataHandler = new RecycleListDataHandler(wordContentQuerier);
  }

  _ComAddWordRelDialogState createState() {
    _ComAddWordRelDialogState res = _ComAddWordRelDialogState();
    
    return res;
  }
}

class _ComAddWordRelDialogState extends State<ComAddWordRelDialog> {

  List<WordDto> _dataSource = [];

  late EasyRefreshController _controller;
  TextEditingController _wordEditingController = TextEditingController();
  TextEditingController _meanEditingController = TextEditingController();
  FocusNode _wordFocusNode = FocusNode();
  FocusNode _meanFocusNode = FocusNode();
  double _heightOffset = 0;
  final double _topBarHeight = 90;
  final double _bottomBtnHeight = 60;

  @override
  void initState() {
    _controller = EasyRefreshController(
        controlFinishLoad: true,
        controlFinishRefresh: true
    );

    if (widget.initString != null) {
      _wordEditingController.text = widget.initString!;
      widget.initString = null;
    _searchData();
    }
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    _wordEditingController.dispose();
    _meanEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _heightOffset = MediaQuery.of(context).size.height * 0.2;
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height-_heightOffset,
      child: Column(
          children: [
            _topBar(),
            Expanded (
              child: Container(
                height: MediaQuery.of(context).size.height-_heightOffset-_topBarHeight-_bottomBtnHeight,
                child: _dataSource.length == 0 ? Center(child: Text(""),)
                    : EasyRefresh(
                  child: ListView.builder(
                    itemCount: _dataSource.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _listItem(index);
                    },
                  ),
                  onLoad: _makeData,
                  controller: _controller,
                ),
              ),
            ),
            Container(
              height: _bottomBtnHeight,
              width: 300,
              child: TextButton(
                  onPressed: (){
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    LocStr.of(context)!.close,
                    style: TextStyle(
                      color: Colors.blue,
                    ),
                  )
              ),
            )
          ],
        ),

    );
  }

  _topBar(){
    return Container(
      width: MediaQuery.of(context).size.width,
      height: _topBarHeight,
      color: Colors.blue,
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(width: 10,),
              Expanded(
                child: Container(
                  height: 30,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      child: Center(
                        child: Text(
                          "${widget.curWord!.word}",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 10,),
            ],
          ),
          SizedBox(height: 10,),
          Container(
            height: 30,
            child: Row(
              children: [
                SizedBox(width: 10,),
                GestureDetector(
                    child: Icon(Icons.add_circle_outlined),
                    onTap: (){
                      _showAddWordDialog(context);
                    }
                ),
                SizedBox(width: 10,),
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5)
                    ),
                    child: TextField(
                      focusNode: _wordFocusNode,
                      controller: _wordEditingController,
                      style: TextStyle(fontSize: 16),
                      maxLines: 1,
                      onChanged: (text) async {
                        await _searchData();
                      },
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                        hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                        hintText: LocStr.of(context)!.word,
                        enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0x00FF0000))
                        ),
                      ),
                    )
                  ),
                ),
                SizedBox(width: 5,),
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5)
                    ),
                    child: TextField(
                      focusNode: _meanFocusNode,
                      controller: _meanEditingController,
                      style: TextStyle(fontSize: 16),
                      maxLines: 1,
                      onChanged: (text) async {
                        await _searchData();
                      },
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                        hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
                        hintText: LocStr.of(context)!.meaning,
                        enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0x00FF0000))
                        ),
                      ),
                    )
                  ),
                ),
                SizedBox(width: 10,),
                GestureDetector(
                  child: Icon(Icons.cancel, color: Colors.grey,),
                  onTap: (){
                    _wordEditingController.clear();
                    _meanEditingController.clear();
                    _wordFocusNode.unfocus();
                    _meanFocusNode.unfocus();
                    _dataSource.clear();
                    //_makeData();
                  },
                ),
                SizedBox(width: 10,),
              ],
            ),
          )
        ],
      ),
    );
  }

  _listItem(int index){
    return GestureDetector (
        onTap: () {
          WordBiz.shared.playWordSound(_dataSource[index].word!.word!);
        },
        onLongPress: (){
          FocusScope.of(context).unfocus();
          _showDetail(_dataSource[index].word!);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            children: [
              SizedBox(height: 5,),
              Container(
                  width: MediaQuery.of(context).size.width-30,
                  height: 30,
                  child: Row(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Text(
                            _dataSource[index].word!.word!,
                            style: TextStyle(
                                color: Colors.black,
                                fontSize: 20,
                                fontWeight:
                                FontWeight.w400
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 20,),
                      FutureBuilder(
                        future: _initBookContentCubitAsync(widget.curWord, _dataSource[index]),
                        builder: (BuildContext context, AsyncSnapshot<BookContentCubit> snapshot) {
                          if (snapshot.connectionState == ConnectionState.done) {
                            return BlocProvider(
                              create: (ctx) =>
                              BookContentCubit.withWord(widget.curWord)
                                ..checkAddedAndRelated(_dataSource[index]),
                              child: BlocBuilder<BookContentCubit, BookContentExistedState>(
                                builder: (context, state) {
                                  return Row(
                                    children: [
                                      toggleAddStudyBtn (context, state, index),
                                      SizedBox(width: 15,),
                                      toggleRelatedBtn (context, state, index),
                                    ]
                                  );
                                },
                              ),
                            );
                          }else {
                            return Center(
                              child: Container(),
                            );
                          }
                        }
                      ),
                      SizedBox(width: 1,),
                    ],
                  )
              ),
              Container(
                width: MediaQuery.of(context).size.width-30,
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(5)
                ),
                child: Text(_dataSource[index].word!.meaning!),
              ),
              SizedBox(height: 5,),
              Divider()
            ],
          ),
        )
    );
  }

  Future<void> _showAddWordDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AddWordToDictDialog(wordStr: _wordEditingController.text,
            meaningStr: _meanEditingController.text,
            callBack: () => _searchData());
      },
    );
  }
  toggleAddStudyBtn (context, state, index) {
    if (state.isExisted == null) {
      return Container(
        width: 20,
        height: 20,
      );
    } else if (state.isExisted == true) {
      return GestureDetector(
        child: Container(
          width: 20,
          height: 20,
          child: Icon( Icons.remove_circle_outline , color: Colors.red),
        ),
        onTap: (){
          BlocProvider.of<BookContentCubit>(context).addBtnClicked(context, _dataSource[index]);
        },
      );
    } else {
      return GestureDetector(
        child: Container(
          width: 20,
          height: 20,
          child: Icon(Icons.add_circle_outlined , color: Colors.green),
        ),
        onTap: (){
          BlocProvider.of<BookContentCubit>(context).addBtnClicked(context, _dataSource[index]);
        },
      );
    }
  }

  toggleRelatedBtn (context, state, index) {
    if (state.isRelated == null) {
      return Container(
        width: 20,
        height: 20,
      );
    } else if (state.isRelated == true) {
      return
        GestureDetector(
          child: Container(
            width: 20,
            height: 20,
            child: Icon(Icons.insert_link, color: Colors.green),
          ),
          onTap: () async {
            await BlocProvider.of<BookContentCubit>(context)
                .deleteRelationBtn(context,
                widget.curWord!, _dataSource[index], index);
            if (widget.addHandler != null){
              await widget.addHandler!();
            }
          },
        );
    } else {
      return GestureDetector(
        child: Container(
          width: 20,
          height: 20,
          child: Icon(Icons.insert_link, color: Colors.grey[400]),
        ),
        onTap: () async {
          await BlocProvider.of<BookContentCubit>(context).addRelationBtn(context,
              widget.curWord!, _dataSource[index], index);
          if (widget.addHandler != null){
            await widget.addHandler!();
          }
        },
      );
    }
  }

  Future<Null> _searchData() async {
    //因为是异步查询，当连续输入时，第一个查询还没完时，后续查询只置searchWaiting标记，多次查询都只记一次，用do循环实现最后一次的补查
    if (widget.isSearching == true) {
      if (widget.searchWaiting == true) {
        return;
      }
      else {
        widget.searchWaiting = true;
        return;
      }
    }

    do {
      widget.isSearching = true;
      widget.searchWaiting = false;
      Map<String, Object> param = {};
      param["searchString"] = _wordEditingController.text;
      param["searchString2"] = _meanEditingController.text;
      param["curWord"] = widget.curWord!;
      widget.recycleListDataHandler!.listQueryCallable!.setParam(param);
      widget.recycleListDataHandler!.reset(0);

      await widget.recycleListDataHandler!.searchContent(1);
      if (mounted) {
        setState(() {
          if (CollectionUtil.nonEmpty(widget.recycleListDataHandler!
              .itemList)) {
            _dataSource = widget.recycleListDataHandler!.itemList!.cast<WordDto>().toList();
          } else {
            _dataSource = [];
          }
        });
      }

      //发现连续查询同一张表会卡死，这里睡500毫秒
      await Future.delayed(Duration(milliseconds: 500));
      widget.isSearching = false;
    } while (widget.searchWaiting == true);

    _controller.finishRefresh();
    _controller.finishLoad();
  }

  Future<Null> _makeData() async {
    widget.isSearching = true;
    await widget.recycleListDataHandler!.searchContent(1);
    if (mounted){
      setState(() {
        if (CollectionUtil.nonEmpty(widget.recycleListDataHandler!.itemList)) {
          _dataSource = widget.recycleListDataHandler!
              .itemList!.cast<WordDto>().toList();
        } else {
          _dataSource = [];
        }
      });
    }
    widget.isSearching = false;
    _controller.finishLoad();
  }
  Future<BookContentCubit> _initBookContentCubitAsync(Word? sourceWord, WordDto wordDto) async {
    final cubit = BookContentCubit();
    while (widget.isSearching == true)
      await Future.delayed(Duration(milliseconds: 100));

    await cubit.checkAddedAndRelated(wordDto);
    return cubit;
  }

  _showDetail(Word entity){
    showDialog(
        context: context,
        builder: (ctx) {
          return Dialog(
            insetPadding: EdgeInsets.only(top: 15, left: 15, bottom: 25, right: 15),
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10)
            ),
            child: SingleWordDialog(entity, CommonSingleWordBtnCallback()),
          );
        }
    );
  }
}
