
import 'package:words/ui/widget/single_word_dialog/single_word_btn_callable.dart';

import '../../../biz/word_biz.dart';
import '../../../domain/cur_study_record.dart';

class CommonSingleWordBtnCallback implements SingleWordBtnCallable {
  void processRemberBtn(CurStudyRecord curStudyRecord) {
    //单独打开的单词，点rember按钮，什么都不做
    return;
  }

  void processForgetBtn(CurStudyRecord curStudyRecord) {
    //传进来的curStudyRecord，有可能是从库中取的，也有可能是直接用studyRecord生成的（如在singleWord对话框中生成）
    //不论哪种情况，都直接处理forget功能
    WordBiz.shared.processForgetWord(curStudyRecord);
  }

  void processBlurryBtn(CurStudyRecord curStudyRecord) {
    //单独打开的单词，点blurry按钮，什么都不做
    return;
  }
}