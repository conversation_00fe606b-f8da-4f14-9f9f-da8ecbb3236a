﻿select count(*),study_level,cur_ebbinghaus_str from study_record group by study_level,cur_ebbinghaus_str order by 2,3

select count(*),study_level,cur_ebbinghaus_str from study_record group by study_level order by 2

select STRFTIME('%Y-%m-%d', next_study_date/1000 + 28800 - 14400, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by date asc;

select STRFTIME('%Y-%m-%d', next_study_date/1000 + 28800 - 14400, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by count desc, date asc;

select STRFTIME('%Y-%m-%d', next_study_date/1000 + 28800 - 14400, 'unixepoch') as date, cur_ebbinghaus_str, count(*) as count from STUDY_RECORD group by date,cur_ebbinghaus_str order by date asc;

select STRFTIME('%Y-%m-%d', min_next_study_date/1000 + 28800 - 14400, 'unixepoch') as date, cur_ebbinghaus_str, count(*) as count from STUDY_RECORD group by date,cur_ebbinghaus_str order by date asc;

select STRFTIME('%Y-%m-%d', min_next_study_date/1000 + 28800 - 14400, 'unixepoch') as date, cur_ebbinghaus_str, count(*) as count from CUR_STUDY_RECORD where first_judge = 1 group by date,cur_ebbinghaus_str order by date asc;

select STRFTIME('%Y-%m-%d %H', next_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by date asc;

select STRFTIME('%Y-%m-%d %H', next_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by count desc , date asc;

select STRFTIME('%Y-%m-%d %H', min_next_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by date asc;

select STRFTIME('%Y-%m-%d %H', min_next_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by count desc, date asc;

select STRFTIME('%Y-%m-%d %H:%M', min_next_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by date asc;

select STRFTIME('%Y-%m', first_study_date/1000 + 28800, 'unixepoch') as date, count(*) as count from STUDY_RECORD group by date order by date desc;