part of 'package:words/ui/book/book_content_cubit.dart';

abstract class BookContentState {}

class BookContentExistedState extends BookContentState {
  bool? isExisted;
  bool? isRelated;
  int? operationIndex;

  BookContentExistedState init() {
    return BookContentExistedState();
  }

  BookContentExistedState clone() {
    BookContentExistedState res = BookContentExistedState();
    res.isExisted = isExisted;
    res.isRelated = isRelated;
    return res;
  }
}
