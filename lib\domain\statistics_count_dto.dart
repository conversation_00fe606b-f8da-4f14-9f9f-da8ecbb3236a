import 'package:json_annotation/json_annotation.dart';
part 'statistics_count_dto.g.dart';

@JsonSerializable()
class StatisticsCountDto {
 	@JsonKey(name: "DATE")
  String? date;

 	@Json<PERSON>ey(name: "COUNT")
  int? count;

  StatisticsCountDto();

  StatisticsCountDto.init(this.date, this.count);
  

  factory StatisticsCountDto.fromJson(Map<String, dynamic> json) => _$StatisticsCountDtoFromJson(json);

	Map<String, dynamic> toJson() => _$StatisticsCountDtoToJson(this);
}