import 'package:flutter/material.dart';
import 'package:words/l10n/loc_str.dart';
import 'package:words/ui/book/book_page.dart';
import 'package:words/ui/dict/dict_page.dart';
import 'package:words/ui/home/<USER>';
import 'package:words/ui/setting/setting_page.dart';

class RootPage extends StatefulWidget {
  @override
  _RootPageState createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> {

  int _pageIndex = 1;

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          items: [
            BottomNavigationBarItem(
              label: LocStr.of(context)!.study!,
              icon: Icon(Icons.school),
            ),
            BottomNavigationBarItem(
              label: LocStr.of(context)!.dictionary!,
              icon: Icon(Icons.local_library),
            ),
            BottomNavigationBarItem(
              label: LocStr.of(context)!.book!,
              icon: Icon(Icons.book),
            ),
            BottomNavigationBarItem(
              label: LocStr.of(context)!.setting!,
              icon: Icon(Icons.settings),
            ),
          ],
          currentIndex: _pageIndex,
          onTap: _selectPage,
        ),
        body:  IndexedStack(
          children: [
            HomePage(),
            DictPage(),
            BookPage(),
            SettingPage(),
          ],
          index: _pageIndex,
        )
    );
  }

  _selectPage(index){
    this.setState(() {
      _pageIndex = index;
    });
  }

}