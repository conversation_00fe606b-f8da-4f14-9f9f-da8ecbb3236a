import 'dart:collection';

import 'package:words/domain/cur_study_record.dart';

class LimitedList<E> extends ListBase<E> {
  int size;
  List<E> _innerList = <E>[];

  LimitedList(int size) : size = size;

  @override
  int get length => _innerList.length;

  @override
  set length(int value) {
    _innerList.length = value;
  }

  @override
  E operator [](int index) => _innerList[index];

  @override
  void operator []=(int index, E value) {
    _innerList[index] = value;
  }

  @override
  bool add(E element) {
    _innerList.add(element);
    if (_innerList.length > size) {
      _innerList.removeAt(0);
    }
    return true;
  }

  @override
  void addAll(Iterable<E> iterable) {
    for (E element in iterable) {
      add(element);
    }
  }

}

