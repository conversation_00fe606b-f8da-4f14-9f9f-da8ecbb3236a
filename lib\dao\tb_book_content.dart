import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/book.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/domain/book_content.dart';

class TbBookContent extends DBBaseProvider {

  @override
  String get tableName => "BOOK_CONTENT";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        BOOK_ID TEXT,
        CONTENT_SEARCH_ID TEXT,
        FREQUENCY INTEGER,
        SEQUENCE INTEGER,
        CONTENT_ID VARCHAR(1024)
        )
        ''';
  }



}