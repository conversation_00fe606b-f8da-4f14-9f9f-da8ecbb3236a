import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:words/domain/word.dart';

import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class WordDto {
  Word? word;
  bool? added;
  bool? related;
  bool? isInDict;
  bool? editable = false;
  String? showContent;
  int? relateStep;
  int? sequence;

  WordDto(){
    word = Word();
  }

  WordDto.fromWord(Word word, bool added, bool related) {
    this.word = word;
    this.setIsInDictState(true);
    calEditable(false);
    setAddedState(added);
    setRelatedState(related);
  }
  WordDto.simple (Word word) {
    this.word = word;
    this.setIsInDictState(true);
    calEditable(false);
  }

  WordDto.onlyWord(this.word) {
    this.setIsInDictState(true);
    calEditable(false);
  }

  WordDto.noneWord() {
    this.showContent = "";
  }

  String? noneWordString() {
    this.showContent = "";
    return this.showContent;
  }

  String? onlyWordString() {
    if (this.word == null) {
      this.showContent = "";
      return this.showContent;
    }
    this.showContent = word!.word;
    return this.showContent;
  }

  String? fullWordString() {
    if (this.word == null) {
      this.showContent = "";
      return this.showContent;
    }
    this.showContent = "${word!.word}      ${word!.meaning}";
    return this.showContent;
  }

  String? buildCurStepShowString(int showStep) {
    switch (showStep) {
      case 0:
        noneWordString();
        break;
      case 1:
        onlyWordString();
        break;
      case 2:
        fullWordString();
        break;
      default:
        noneWordString();
        break;
    }
    return this.showContent;
  }

  void setIsInDictState(bool isInDict) {
    this.isInDict = isInDict;
  }

  static List<WordDto>? convertToWordDtoList(List<Word>? wordList) {
    if (wordList == null) return null;
    List<WordDto> res = [];
    for (Word word in wordList) {
      res.add(WordDto.fromWord(word, false, false));
    }
    return res;
  }

  void setAddedState(bool added) {
    this.added = added;
  }

  void setRelatedState(bool related) {
    this.related = related;
  }

  void calEditable(bool forceEdit) {
      this.editable = calEditableTool(this.word, forceEdit);
  }

  static bool calEditableTool(Word? vWord, bool forceEdit) {
    if (vWord == null) {
      return false;
    }
    if (forceEdit == true)
      return true;
    else
      return SystemConfig.user!.id == vWord!.ownerId;
  }

  static void buildStepShowContent4List(List<WordDto>? wordDtos, int step) {
    if (CollectionUtil.isEmpty(wordDtos))
      return;

    for (WordDto w in wordDtos!) {
      w.buildCurStepShowString(step);
    }
  }

}
