import 'package:json_annotation/json_annotation.dart';
import 'package:words/domain/support/is_json_serialable.dart';
import 'dart:convert';

import 'package:words/commonSupport/utils/type_serial_util.dart';

part 'word.g.dart';

@JsonSerializable()
class Word implements IsJsonSerialable{
	@Json<PERSON><PERSON>(name: "ID")
	String? id;

	@Json<PERSON><PERSON>(name: "SEARCH_ID")
	String? searchId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "OWNER_ID")
	String? ownerId;

	@J<PERSON><PERSON><PERSON>(name: "WORD")
	String? word;

	@<PERSON><PERSON><PERSON><PERSON>(name: "MEANING")
	String? meaning;

	@<PERSON><PERSON><PERSON><PERSON>(name: "USER_EDITED", fromJson: TypeSerialUtil.dynamicToBool, toJson: TypeSerialUtil.boolToInt)
	bool? userEdited;

	@J<PERSON><PERSON><PERSON>(name: "CATEGORY")
	String? category;

	@Json<PERSON>ey(name: "FREQUENCY")
	int? frequency;

	Word(){}

	Word.fromParams(String? word, String? meaning) {
		this.word = word;
		this.meaning = meaning;
		this.searchId = word == null? null: word.trim().toLowerCase();
	}

	factory Word.fromJson(Map<String, dynamic> json) => _$WordFromJson(json);

	@override
	Map<String, dynamic> toJson() => _$WordToJson(this);

	@override
	fromJson(param) {
		return Word.fromJson(param);
	}

	@override
	String toString() {
		return jsonEncode(this);
	}

  clone() {
		Word reslut = fromJson(this.toJson());
		return reslut;
	}

  void copyNonNullTo(Word targetWord) {
		targetWord.id = this.id == null ? targetWord.id : this.id;
		targetWord.searchId = this.searchId == null ? targetWord.searchId : this.searchId;
		targetWord.ownerId = this.ownerId == null ? targetWord.ownerId : this.ownerId;
		targetWord.word = this.word == null ? targetWord.word : this.word;
		targetWord.meaning = this.meaning == null ? targetWord.meaning : this.meaning;
		targetWord.frequency = this.frequency == null ? targetWord.frequency : this.frequency;
		targetWord.userEdited = this.userEdited == null ? targetWord.userEdited : this.userEdited;
		targetWord.category = this.category == null ? targetWord.category : this.category;
	}

}