import 'cur_study_record.dart';

class CurStudyRcdBuffer {
  Map<String, CurStudyRecord> buffer = {};
  int bufferIndex = 0;
  List<CurStudyRecord> recordList = [];

  CurStudyRecord? getCurStudyRecordFromBuffer() {
    if (buffer.isEmpty) {
      return null;
    }
    if (bufferIndex == null || bufferIndex == buffer.length) {
      bufferIndex = 0;
    }
    if (recordList != null && recordList.length > 0) {
      return recordList[bufferIndex++];
    }
    return null;
  }

  void add2buff(CurStudyRecord currentStudyRecord) {
    if (buffer[currentStudyRecord.contentId] == null) {
      buffer[currentStudyRecord.contentId!] = currentStudyRecord;
      recordList = buffer.values.toList();
    }
  }
}