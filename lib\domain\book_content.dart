import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import '../commonSupport/utils/collection_util.dart';

part 'book_content.g.dart';
@JsonSerializable()
class BookContent {
	@J<PERSON><PERSON>ey(name: "ID")
	late String id;

	@<PERSON><PERSON><PERSON><PERSON>(name: "BOOK_ID")
	late String bookId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "CONTENT_SEARCH_ID")
	late String contentSearchId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "FREQUENCY")
	int? frequency;

	@Json<PERSON>ey(name: "SEQUENCE")
	int? sequence;

	@J<PERSON><PERSON><PERSON>(name: "CONTENT_ID")
	String? contentId;

	BookContent();

	factory BookContent.fromJson(Map<String, dynamic> json) => _$BookContentFromJson(json);

	Map<String, dynamic> toJson() => _$BookContentToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  static List<String>? convert2ContentSearchIdList(List<BookContent> bookContents) {
		if (CollectionUtil.isEmpty(bookContents))
			return null;
		List<String> result = [];
		for (BookContent bCt in bookContents!) {
			result.add(bCt.contentSearchId);
		}
		return result;
	}

}