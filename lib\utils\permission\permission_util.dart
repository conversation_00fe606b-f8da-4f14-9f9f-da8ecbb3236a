
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

/**
 * @ClassName ${NAME}
 * 作者: szj
 * 时间: ${DATE} ${TIME}
 * CSDN:https://blog.csdn.net/weixin_44819566
 * 公众号:码上变有钱
 */

class PermissionUtil  {
  PermissionUtil(this._context);
  BuildContext _context ;

  checkPermission({PermissionStatus? status}) async {
    //申请权限

    List<Permission> permissions = []
      ..add(Permission.storage); //外部存储权限

    for (Permission permission in permissions) {

      if (status == null) {
        ///权限状态
        status = await permission.status;
      }

      if (Platform.isAndroid) {
        if (await Permission.manageExternalStorage
            .request()
            .isGranted) {}
        Map<Permission, PermissionStatus> statuses = await [
          Permission.manageExternalStorage
        ].request(); //Permission.manageExternalStorage
      }
      if (!status.isGranted) {
        status = await permission.request();
      }
    }
  }


}
