import 'package:sqflite/sqflite.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/ebbinghaus_point.dart';

import 'package:words/domain/system_property.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class TbSystemProperty extends DBBaseProvider {

  @override
  String get tableName => "SYSTEM_PROPERTY";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        USER_ID TEXT,
        PROPERTY_NAME TEXT,
        PROPERTY_VALUE TEXT,
        PRIMARY KEY ("ID")
        ''';
  }

  Future<List<SystemProperty>?> getSystemProperty(String userId) async {
    List<Map<String, Object?>>? queryList = await commQueryList(
      where: "USER_ID = ?",
      whereArgs: [userId]);

    List<SystemProperty> result = [];
    if (CollectionUtil.isEmpty(queryList))
      return null;
    queryList?.forEach((element) {
      SystemProperty entity = SystemProperty.fromJson(Map<String, dynamic>.of(element));
      result.add(entity);
    });
    return result;
  }

  Future<bool> updateInsert(String? name, String? value, String? userId) async {
    if (StringUtil.isEmpty(userId) || StringUtil.isEmpty(name) || value == null)
      return await false;

    SystemProperty? dbEntity = await queryProperties(name, userId);
    if (dbEntity == null) {
      dbEntity = SystemProperty()
          ..id = UuidUtil.getUuid()
          ..userId = userId
          ..propertyName = name
          ..propertyValue = value;
      await DBManager.shared.tbSystemProperty.insert(dbEntity);
    } else {
      dbEntity.propertyValue = value;
      await DBManager.shared.tbSystemProperty.baseUpdateById(dbEntity.id!, dbEntity.toJson());
    }
    return true;
  }

  Future<SystemProperty?> queryProperties(String? name, String? userId) async {
    var queryRes = await DBManager.shared.tbSystemProperty.commQueryList(
        where: 'USER_ID = ? '
            'AND PROPERTY_NAME = ?',
        whereArgs: [userId, name]
    );
    if (CollectionUtil.isEmpty(queryRes))
      return null;

    SystemProperty entity = SystemProperty.fromJson(Map<String, dynamic>.of(queryRes![0]));
    return entity;
  }

}