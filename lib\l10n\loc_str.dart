import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:words/l10n/messages_all.dart';

class LocStr {
  LocStr(Locale locale) : _localeName = locale.toString();
  final String _localeName;

  static Future<LocStr> load(Locale locale) {
    return initializeMessages(locale.toString())
        .then((Object _) {
      return new LocStr(locale);
    });
  }

  // 静态成员
  static LocStr? of(BuildContext context) {
    return Localizations.of<LocStr>(context, LocStr);
  }

  // 根据 locale 从多语言文件中获取具体的字符串
  String get title {
    return Intl.message(
      'Welcom to Words',
      name: 'title',
      desc: '',
      locale: _localeName,
    );
  }

  String get click {
    return Intl.message(
      'Click',
      name: 'click',
      desc: '',
      locale: _localeName,
    );
  }
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      locale: _localeName,
    );
  }
  String get ok {
    return Intl.message(
      'Ok',
      name: 'ok',
      desc: '',
      locale: _localeName,
    );
  }
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      locale: _localeName,
    );
  }
  String get help {
    return Intl.message(
      'Help',
      name: 'help',
      desc: '帮助',
      locale: _localeName,
    );
  }
  String get warning {
    return Intl.message(
      'Warning',
      name: 'warning',
      desc: '警告',
      locale: _localeName,
    );
  }
  String get study {
    return Intl.message(
      'Study',
      name: 'study',
      desc: '',
      locale: _localeName,
    );
  }
  String get dictionary {
    return Intl.message(
      'Dictionary',
      name: 'dictionary',
      desc: '',
      locale: _localeName,
    );
  }
  String get book {
    return Intl.message(
      'Book',
      name: 'book',
      desc: '',
      locale: _localeName,
    );
  }
  String get setting {
    return Intl.message(
      'Setting',
      name: 'setting',
      desc: '',
      locale: _localeName,
    );
  }
  String get remember {
    return Intl.message(
      'Remember',
      name: 'remember',
      desc: '',
      locale: _localeName,
    );
  }

  String get forgot {
    return Intl.message(
      'Forgot',
      name: 'forgot',
      desc: '',
      locale: _localeName,
    );
  }

  String get uncertain {
    return Intl.message(
      'Uncertain',
      name: 'uncertain',
      desc: '',
      locale: _localeName,
    );
  }
  String get word {
    return Intl.message(
      'word',
      name: 'word',
      desc: '',
      locale: _localeName,
    );
  }
  String get meaning {
    return Intl.message(
      'meaning',
      name: 'meaning',
      desc: '',
      locale: _localeName,
    );
  }
  String get addNewWord {
    return Intl.message(
      'Add New Word',
      name: 'addNewWord',
      desc: '添加新单词',
      locale: _localeName,
    );
  }

  String get editWord {
    return Intl.message(
      'Edit Word',
      name: 'editWord',
      desc: '编辑单词',
      locale: _localeName,
    );
  }
  String get details {
    return Intl.message(
      'Details',
      name: 'details',
      desc: '详情',
      locale: _localeName,
    );
  }
  String get relate {
    return Intl.message(
      'Relate',
      name: 'relate',
      desc: '关联',
      locale: _localeName,
    );
  }
  String get bookmark {
    return Intl.message(
      'bookmark',
      name: 'bookmark',
      desc: '书签',
      locale: _localeName,
    );
  }
  String get hint {
    return Intl.message(
      'Hint',
      name: 'hint',
      desc: '提示',
      locale: _localeName,
    );
  }
  String confirmMoveBookmark(String number) {
    return Intl.message(
      'Move the bookmark to ${number}?',
      name: 'confirmMoveBookmark',
      desc: '确认把书签移到某处吗',
      args: [number],
      locale: _localeName,
    );
  }
  String get generalSetting {
    return Intl.message(
      'General Setting',
      name: 'generalSetting',
      desc: '通用设置',
      locale: _localeName,
    );
  }
  String get shareToBackupData {
    return Intl.message(
      'Share to backup data',
      name: 'shareToBackupData',
      desc: '通过分享备份数据',
      locale: _localeName,
    );
  }
  String get newWordFirst {
    return Intl.message(
      'New words first',
      name: 'newWordFirst',
      desc: '生词优先',
      locale: _localeName,
    );
  }
  String get defaultToMute {
    return Intl.message(
      'Mute on startup',
      name: 'defaultToMute',
      desc: '启动时默认静音',
      locale: _localeName,
    );
  }
  String get downloadVoice {
    return Intl.message(
      'Download voice',
      name: 'downloadVoice',
      desc: '下载语音',
      locale: _localeName,
    );
  }
  String get autoDownloadVoice {
    return Intl.message(
      'Auto download voice',
      name: 'autoDownloadVoice',
      desc: '自动下载语音',
      locale: _localeName,
    );
  }
  String downloadVoiceUrlHint(String placehold) {
    return Intl.message(
      'URL for download the audios, be sure contain the placeholder: ${placehold}.',
      name: 'downloadVoiceUrlHint',
      desc: '下载语音Url，单词用 \${word} 代替',
      args: [placehold],
      locale: _localeName,
    );
  }

  String get recallPeriod {
    return Intl.message(
      'Recall Period',
      name: 'recallPeriod',
      desc: '记忆周期设置',
      locale: _localeName,
    );
  }
  String get recallPeriodHint {
    return Intl.message(
      "If you are not familiar with the meaning of this setting, please do not modify the configuration.",
      name: 'recallPeriodHint',
      desc: '如果您不了解本项配置的含义，请不要修改配置',
      locale: _localeName,
    );
  }
  String get initReciteSet {
    return Intl.message(
      "Initial Recitation Period Settings",
      name: 'initReciteSet',
      desc: '首次背诵周期设置',
      locale: _localeName,
    );
  }
  String get reviewIntervalSet {
    return Intl.message(
      "Review Interval Period Settings",
      name: 'reviewIntervalSet',
      desc: '复习间隔周期设置',
      locale: _localeName,
    );
  }
  String get restoreWarning {
    return Intl.message(
      "Warning: Restoring data will overwrite your existing study records! Please make sure to back up your original data.",
      name: 'restoreWarning',
      desc: '警告：还原数据会覆盖您原有的学习记录数据！请务必备份好原有数据',
      locale: _localeName,
    );
  }
  String get clickToBackup {
    return Intl.message(
      "Click here to back up by sharing.",
      name: 'clickToBackup',
      desc: '点击这里，通过分享备份',
      locale: _localeName,
    );
  }
  String get inputToConfirmRestore {
    return Intl.message(
      'Input "restore" to confirm.',
      name: 'inputToConfirmRestore',
      desc: '如果确认还原数据，请输入 "restore"',
      locale: _localeName,
    );
  }
  String get restoreData {
    return Intl.message(
      'Restore Data',
      name: 'restoreData',
      desc: '还原数据',
      locale: _localeName,
    );
  }
  String get restartNow {
    return Intl.message(
      'Restart Now',
      name: 'restartNow',
      desc: '立刻重启',
      locale: _localeName,
    );
  }
  String get incorrectInput {
    return Intl.message(
      'Incorrect Input',
      name: 'incorrectInput',
      desc: '输入错误',
      locale: _localeName,
    );
  }
  String get restoreFailNoFile {
    return Intl.message(
      'Restoration failed: No shared data file received.',
      name: 'restoreFailNoFile',
      desc: '恢复失败：没有收到共享数据文件',
      locale: _localeName,
    );
  }
  String get restoreFailInvalidFile {
    return Intl.message(
      'Restoration failed: Invalid data file, please check the data file.',
      name: 'restoreFailInvalidFile',
      desc: '恢复失败：不是合法数据文件，请检查数据文件',
      locale: _localeName,
    );
  }
  String get restoreSuccess {
    return Intl.message(
      'Restoration successful! Please restart the app.',
      name: 'restoreSuccess',
      desc: '恢复成功！请重启app',
      locale: _localeName,
    );
  }
  String get restoreFailCopyError {
    return Intl.message(
      'Restoration failed: Error in copying data, please restore again.',
      name: 'restoreFailCopyError',
      desc: '恢复失败：拷贝数据出错，请重新恢复',
      locale: _localeName,
    );
  }

  String get restoreFailPlsRetry {
    return Intl.message(
      'Restoration failed, please restore again.',
      name: 'restoreFailPlsRetry',
      desc: "备份失败！请重新备份",
      locale: _localeName,
    );
  }

  String get wordNotInDict {
    return Intl.message(
      'The word is not in the dictionary, please add it to the dictionary first.',
      name: 'wordNotInDict',
      desc: "单词不在词典中，请先加入词典",
      locale: _localeName,
    );
  }

  String get bookmarkNotAdded {
    return Intl.message(
      'This book does not have any bookmarks yet. Press and hold the bookmark icon to add a bookmark.',
      name: 'bookmarkNotAdded',
      desc: "本书尚未添加书签，长按书签图标添加书签",
      locale: _localeName,
    );
  }

  String get wordStartedCantDelete {
    return Intl.message(
      'The word has started being studied and cannot be deleted.',
      name: 'wordStartedCantDelete',
      desc: "单词已开始学习，不能删除",
      locale: _localeName,
    );
  }

  String get cantRelateToItself {
    return Intl.message(
      'Cannot relate the word with itself.',
      name: 'cantRelateToItself',
      desc: "不能对自己关联",
      locale: _localeName,
    );
  }

  String get deletedSuccessfully {
    return Intl.message(
      'Deleted Successfully!',
      name: 'deletedSuccessfully',
      desc: "删除成功！",
      locale: _localeName,
    );
  }
  String get deletionFailed {
    return Intl.message(
      'Deletion failed, please try again!',
      name: 'deletionFailed',
      desc: "删除失败，请重试！",
      locale: _localeName,
    );
  }

  String get noWordBeingStudied {
    return Intl.message(
      'There are no words currently being studied!',
      name: 'noWordBeingStudied',
      desc: "无正在学习的单词！",
      locale: _localeName,
    );
  }

  String get noNewWordsToAdd {
    return Intl.message(
      'No new words available to add!',
      name: 'noNewWordsToAdd',
      desc: "无新词可添加！",
      locale: _localeName,
    );
  }

  String nWordAdded(String number) {
    return Intl.message(
      '${number} new words have been added.?',
      name: 'nWordAdded',
      desc: "已添加 n 个新词",
      args: [number],
      locale: _localeName,
    );
  }

  String invalidDownloadUrl(String placehold) {
    return Intl.message(
      'Invalid URL, the URL must contain the ${placehold} ',
      name: 'invalidDownloadUrl',
      desc: "无效URL，URL中必包含 \${word} 标记",
      args: [placehold],
      locale: _localeName,
    );
  }

  String modifyFailedCheck(String msg) {
    return Intl.message(
      'Modification failed, please check ${msg} ',
      name: 'modifyFailedCheck',
      desc: "修改失败，请检查 \${msg}",
      args: [msg],
      locale: _localeName,
    );
  }

  String get modifySuccess {
    return Intl.message(
      'Modification successful.',
      name: 'modifySuccess',
      desc: "修改成功",
      locale: _localeName,
    );
  }

  String get invalidWordOrMeaning {
    return Intl.message(
      'Invalid word or meaning!',
      name: 'invalidWordOrMeaning',
      desc: "无效的单词或词义！",
      locale: _localeName,
    );
  }

  String get addSuccess {
    return Intl.message(
      'Added successfully!',
      name: 'addSuccess',
      desc: "添加成功",
      locale: _localeName,
    );
  }

  String get wordNotStarted {
    return Intl.message(
      'The word has not started being studied yet.',
      name: 'wordNotStarted',
      desc: "单词尚未开始学习！",
      locale: _localeName,
    );
  }

  String get studyRecordUpdated {
    return Intl.message(
      'The study record updated.',
      name: 'studyRecordUpdated',
      desc: "复习记录已更新",
      locale: _localeName,
    );
  }

  String warnDeleteWordStudyRecord(String word) {
    return Intl.message(
      'Deleting the study record of "${word}" will erase learning history for this word!',
      name: 'warnDeleteWordStudyRecord',
      desc: '删除 {word} 的学习记录，将会清除这个单词的所有学习历史！',
      args: [word],
      locale: _localeName,
    );
  }

  String get inputDeleteToDelete {
    return Intl.message(
      'Input "delete" to confirm.',
      name: 'inputDeleteToDelete',
      desc: '如果确认删除，请输入 delete',
      locale: _localeName,
    );
  }

  String confirmDeleteWordVoice(String word) {
    return Intl.message(
      'Really want to delete the voice file of ${word}?',
      name: 'confirmDeleteWordVoice',
      desc: '确认删除 {word} 的语音文件吗？',
      args: [word],
      locale: _localeName,
    );
  }
}
