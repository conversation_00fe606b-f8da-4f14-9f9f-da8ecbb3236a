import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/word.dart';
import 'package:words/dto/word_dto.dart';
import 'package:words/domain/word_relation.dart';

import 'package:words/common/system_config.dart';
import 'package:words/domain/support/has_string_properties.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

class WordRelationManager {
  //单例模式
  factory WordRelationManager() => _shared();
  static WordRelationManager get shared => _shared();
  static WordRelationManager? _instance;
  WordRelationManager._();
  static WordRelationManager _shared() {
    if (_instance == null) {
      _instance = WordRelationManager._();
    }
    return _instance!;
  }

  Future<List<WordRelation>?> queryAllDirectRelationForWord(Word word) async {
    if (word == null) return null;

    List<Map<String, Object?>>? queryList = await DBManager.shared.tbWordRelation.commQueryList(
        where: 'OWNER_ID = ? AND RELATION_ID = ?',
        whereArgs: [SystemConfig.user!.id, word.id]
    );
    List<WordRelation> masterRelations = [];
    if (CollectionUtil.nonEmpty(queryList)) {
      queryList?.forEach((element) {
        WordRelation entity = WordRelation.fromJson(
            Map<String, dynamic>.of(element));
        masterRelations.add(entity);
      });
    }


    queryList = await DBManager.shared.tbWordRelation.commQueryList(
        where: 'OWNER_ID = ? AND WORD_ID = ?',
        whereArgs: [SystemConfig.user!.id, word.id]
    );
    List<WordRelation> slaveRelations = [];
    if (CollectionUtil.nonEmpty(queryList)) {
      queryList?.forEach((element) {
        WordRelation entity = WordRelation.fromJson(
            Map<String, dynamic>.of(element));
        slaveRelations.add(entity);
      });
    }

    List<WordRelation> res = [];
    if (CollectionUtil.nonEmpty(masterRelations)) res.addAll(masterRelations);
    if (CollectionUtil.nonEmpty(slaveRelations)) res.addAll(slaveRelations);

    return res;
  }

  void removeWordDtoListFromList(List<WordDto>? sourceWordDtos, List<WordDto>? removeWordDtos) {
    if (CollectionUtil.isEmpty(sourceWordDtos) || CollectionUtil.isEmpty(removeWordDtos)) {
      return;
    }

    for (WordDto removeDto in removeWordDtos!) {
      List<WordDto> duplicated = [];
      for (WordDto sourceDto in sourceWordDtos!) {
        if (sourceDto.word!.id == removeDto.word!.id) {
          duplicated.add(sourceDto);
        }
      }
      sourceWordDtos.removeWhere((sourceDto) => duplicated.contains(sourceDto));
    }
  }

  Future<List<WordRelation>?> queryRelationForWordPair(Word? masterWord, Word? slaveWord) async {
    if (masterWord == null || slaveWord == null )
      return null;
    //取所有masterWord所在的relation
    List<Map<String, Object?>>? masterRelations  = await DBManager.shared.tbWordRelation.commQueryList(
        where: 'OWNER_ID = ? '
            'AND RELATION_ID = ?'
            'AND WORD_ID = ?',
        whereArgs: [SystemConfig.user!.id, masterWord.id, slaveWord.id]
    );

    List<Map<String, Object?>>? slaveRelations  = await DBManager.shared.tbWordRelation.commQueryList(
        where: 'OWNER_ID = ? '
            'AND WORD_ID = ?'
            'AND RELATION_ID = ?',
        whereArgs: [SystemConfig.user!.id, masterWord.id, slaveWord.id]
    );

    List<WordRelation> res = [];
    if (CollectionUtil.nonEmpty(masterRelations)) {
      masterRelations?.forEach((element) {
        WordRelation entity = WordRelation.fromJson(
            Map<String, dynamic>.of(element));
        res.add(entity);
      });
    }
    if (CollectionUtil.nonEmpty(slaveRelations)) {
      slaveRelations?.forEach((element) {
        WordRelation entity = WordRelation.fromJson(
            Map<String, dynamic>.of(element));
        res.add(entity);
      });
    }

    return res;
  }

  Future<int> insertMultiple(List<WordRelation> relationList) async {
    return await DBManager.shared.tbWordRelation.insertList(relationList);
  }

  insert(WordRelation relation) async {
    return await DBManager.shared.tbWordRelation.insert(relation);
  }

  Future<int> deleteWordRelationList(List<WordRelation>? relations) async {
    if (CollectionUtil.isEmpty(relations))
      return 0;
    int res = 0;
    for (WordRelation rel in relations!) {
      await DBManager.shared.tbWordRelation.removeRelation(rel!.relationId!, rel!.wordId!);
      res ++;
    }
    return res;
  }

  Future<int> deleteWordRelation(WordRelation? relations) async {
    if (relations == null)
      return 0;
    return await DBManager.shared.tbWordRelation.removeRelation(relations!.relationId!, relations!.wordId!);
  }
}