import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/support/is_json_serialable.dart';

import 'package:words/common/system_config.dart';
import 'package:words/domain/word.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';

class WordManager {
  //单例模式
  factory WordManager() => _shared();
  static WordManager get shared => _shared();
  static WordManager? _instance;
  WordManager._();
  static WordManager _shared() {
    if (_instance == null) {
      _instance = WordManager._();
    }
    return _instance!;
  }

  wordListQueryByRelation(String iD) async {
    return await DBManager.shared.tbWord.wordListQueryByRelation(iD);
  }

  Future<Word?> queryWordByIdOfUserNSys(String vId, String userId) async {
    var res = await DBManager.shared.tbWord.commQueryEntityList(Word(),
        where: "ID = ? AND OWNER_ID IN (${StringUtil.toSqlWhereString(SystemConfig.getUserIdLists())})",
        whereArgs: [vId]
    );
    if (CollectionUtil.nonEmpty(res))
      return res![0];
    else
      return null;
  }

  Future<List<Word>?> queryWordByIdList(List<String>? wordIds) async {
    if (CollectionUtil.isEmpty(wordIds))
      return null;
    var queryList = await DBManager.shared.tbWord.commQueryList(
        where: "ID IN (${StringUtil.toSqlWhereString(wordIds!)})"
    );

    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<Word> res = [];
    queryList?.forEach((element) {
      Word entity = Word.fromJson(
          Map<String, dynamic>.of(element));
      res.add(entity);
    });
    return res;
  }

  Future<List<Word>?> queryWordBySearchIdList(List<String>? contentIds) async {
    if (CollectionUtil.isEmpty(contentIds))
      return null;
    var queryList = await DBManager.shared.tbWord.commQueryList(
        where: "SEARCH_ID IN (${StringUtil.toSqlWhereString(contentIds!)})"
    );

    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<Word> res = [];
    queryList?.forEach((element) {
      Word entity = Word.fromJson(
          Map<String, dynamic>.of(element));
      res.add(entity);
    });
    return res;
  }

  Future<List<Word>?> searchWordByPatternByPage(String? serchIdPattern, String? meaningPattern, int? begin, int? actPageSize) async {
    if ((StringUtil.isEmpty(serchIdPattern)) && (StringUtil.isEmpty(meaningPattern))) {
      return null;
    }
    var queryList;
    if (StringUtil.isEmpty(meaningPattern)) {
      queryList = await DBManager.shared.tbWord.commQueryList(
          where: 'OWNER_ID IN (${StringUtil.toSqlWhereString(SystemConfig.getUserIdLists()!)}) '
              'AND SEARCH_ID LIKE ? ',
          whereArgs: [serchIdPattern! + "%"],
          orderBy: 'SEARCH_ID ASC',
          offset: begin,
          limit: actPageSize
      );
    } else if (StringUtil.isEmpty(serchIdPattern)) {
      queryList = await DBManager.shared.tbWord.commQueryList(
          where: 'OWNER_ID IN (${StringUtil.toSqlWhereString(SystemConfig.getUserIdLists()!)}) '
              'AND MEANING LIKE ? ',
          whereArgs: ["%" + meaningPattern! + "%"],
          offset: begin,
          limit: actPageSize
      );
    } else {
      queryList = await DBManager.shared.tbWord.commQueryList(
          where: 'OWNER_ID IN (${StringUtil.toSqlWhereString(SystemConfig.getUserIdLists()!)}) '
              'AND SEARCH_ID LIKE ? '
              'AND MEANING LIKE ? ',
          whereArgs: [serchIdPattern! + "%", "%" + meaningPattern! + "%"],
          offset: begin,
          limit: actPageSize
      );
    }

    if (CollectionUtil.isEmpty(queryList))
      return null;
    List<Word> res = [];
    queryList?.forEach((element) {
      Word entity = Word.fromJson(
          Map<String, dynamic>.of(element));
      res.add(entity);
    });
    return res;
  }

  updateInsertWord(Word newWord) async {
    if (newWord == null)
      return 0;
    if (StringUtil.isEmpty(newWord.id)) {
      newWord.id = UuidUtil.getUuid();
      await DBManager.shared.tbWord.insert(newWord);
      return 1;
    }


    var queryRes = await DBManager.shared.tbWord.commQueryEntityList(Word(),
        where: 'ID = ? ',
        whereArgs: [newWord.id]
    );

    if (CollectionUtil.isEmpty(queryRes)) {
      await DBManager.shared.tbWord.insert(newWord);
      return 1;
    } else {
      Word queryWord = queryRes![0];
      newWord.copyNonNullTo(queryWord);
      await DBManager.shared.tbWord.baseUpdateById(
          newWord!.id!, newWord.toJson());

      return 1;
    }
  }
}