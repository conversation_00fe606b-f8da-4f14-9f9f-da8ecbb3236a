import 'package:sqflite/sqflite.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/support/has_string_properties.dart';
import 'package:words/domain/support/is_json_serialable.dart';
import 'package:words/commonSupport/utils/collection_util.dart';

import 'package:words/commonSupport/utils/string_util.dart';

abstract class DBBaseProvider<T extends IsJsonSerialable> {

  Future<Database?> get dataBase async {
    return await DBManager.shared.getDb();
  }

  bool isTableExist = false;

  String tableName = "";

  // 返回创建表的具体sql
  tableCreateSql();

  //实例插入
  insert(dynamic entity) async {
    if (entity == null)
      return 0;
    Database? db = await dataBase;
    if (entity is HasStringProperties)
      entity.buildStringFromProperty();
    return await db?.insert(tableName, entity.toJson());
  }
  //实例列表插入
  insertList(List<dynamic> entityList) async {
    if (CollectionUtil.isEmpty(entityList))
      return 0;

    Database? db = await dataBase;
    var batch = db?.batch();

    entityList.forEach((element) {
      if (element is HasStringProperties)
        element.buildStringFromProperty();
      batch?.insert(tableName, element.toJson());
    });

    var results = await batch?.commit();
    return entityList.length;
  }

  commQueryEntityById(T t,String id) async {
    dynamic? result;
    var queryRes = baseQueryById(id);
    if (queryRes != null) {
      result = t.fromJson(Map<String, dynamic>.of(queryRes));
        if (result is HasStringProperties)
          result.buildPropertyFromString();
      };
    return result;
  }

  commQueryEntityByIdList(T t,List<String> idList) async {
    final List<Map<String, Object?>>? queryList = await commQueryByIdList(idList);

    List<dynamic> result = [];
    if (CollectionUtil.isEmpty(queryList))
      return null;
    queryList?.forEach((element) {
      dynamic entity = t.fromJson(Map<String, dynamic>.of(element));
      if (entity is HasStringProperties)
        entity.buildPropertyFromString();
      result.add(entity);
    });
    return result;
  }

  commQueryByIdList(List<String> idList) async {
    final Database? db = await dataBase;
    String? idListStr = buildIdListStr(idList);
    if (StringUtil.isEmpty(idListStr))
      return null;
    final List<Map<String, Object?>>? queryList = await db?.query(tableName, where: "ID IN (${StringUtil.toSqlWhereString(idList)})");

    return queryList;
  }

  String? buildIdListStr(List<String> idList) {
    if (CollectionUtil.isEmpty(idList))
      return null;
    String result = "(";
    idList.forEach((element) {
      result += "'" + element + "',";
    });
    result = result.substring(0, result.length - 1) + ")";
    return result;
  }


  Future<List<dynamic>?> commQueryEntityList(T t,{bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset}) async {
    final Database? db = await dataBase;
    final List<Map<String, Object?>>? queryList = await db?.query(tableName,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset);

    List<dynamic> result = [];
    if (CollectionUtil.isEmpty(queryList))
      return null;
    queryList?.forEach((element) {
      dynamic entity = t.fromJson(Map<String, dynamic>.of(element));
      if (entity is HasStringProperties)
        entity.buildPropertyFromString();
      result.add(entity);
    });
    return result;
  }


  Future<List<Map<String, Object?>>?> commQueryList({bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset}) async {
    final Database? db = await dataBase;
    List<Map<String, Object?>>? queryList = await db?.query(tableName,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset);

    return queryList;
  }

  commCount({bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset}) async {
    final Database? db = await dataBase;
    List<Map<String, Object?>>? queryList = await db?.query(tableName,
        distinct: distinct,
        columns: ['COUNT(*)'],
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset);

    return queryList![0]['COUNT(*)'];
  }

  // 单个插入
  baseInsert(Map<String, dynamic> values) async {
    Database? db = await dataBase;
    return await db?.insert(tableName, values);
  }

  // 列表插入
  baseInsertList(List<Map<String, dynamic>> values) async {
    final Database? db = await dataBase;
    try {
      Batch? batch = db?.batch();
      values.forEach((element) {
        batch?.insert(tableName, element);
      });
      await batch?.commit();
    } catch (err) {
      throw(err);
    }
  }

  // 查询列表
  baseQueryList(int page) async {
    final Database? db = await dataBase;
    List<Map<String, Object?>>? maps = await db?.query(tableName, limit: 20, offset: page*20);
    return maps;
  }

  baseRawQueryList(String sql) async {
    final Database? db = await dataBase;
    final List<Map<String, Object?>>? maps = await db?.rawQuery(sql);
    return maps;
  }

  baseQueryById(String id) async {
    Database? db = await dataBase;
    List<Map<String, Object?>>? queryList = await db?.query(tableName, where: "ID = ?", whereArgs: [id]);
    if (CollectionUtil.isEmpty(queryList)) return null;
    return queryList?[0];
  }

  // 删除
  baseDeleteById(String id) async {
    Database? db = await dataBase;
    return await db?.delete(tableName, where: "ID = ?", whereArgs: [id]);
  }

  // 删除
  baseDelete({String? where, List<Object?>? whereArgs}) async {
    Database? db = await dataBase;
    return await db?.delete(tableName,
      where: where,
      whereArgs: whereArgs,);
  }
  // update
  Future<int?> baseUpdateById(String id, Map<String, Object?> values) async {
    final Database? db = await dataBase;
    return await db?.update(tableName, values, where: "ID = ?", whereArgs: [id]);
  }

  // sql前缀
  tableBaseString(String name) {
    return '''
      create table if not exists $name (
    ''';
  }

}