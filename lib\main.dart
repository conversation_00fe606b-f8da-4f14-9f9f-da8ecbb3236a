import 'dart:async';
import 'dart:io';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';
import 'package:words/l10n/loc_str_delegate.dart';
import 'package:words/root.dart';
import 'package:words/utils/permission/permission_util.dart';
import 'package:words/utils/router/word_router.dart';
import 'package:words/utils/tts/tts_utils.dart';

import 'package:words/biz/system_biz.dart';
import 'package:words/common/system_config.dart';
import 'package:words/common/system_const.dart';
import 'package:words/component/ebbinghaus_poing_type.dart';
import 'package:words/domain/user.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  // Define a key for the root widget
  _MyAppState state = _MyAppState();

  @override
  _MyAppState createState() => state;
}

class _MyAppState extends State<MyApp> {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness:
      !kIsWeb && Platform.isAndroid ? Brightness.dark : Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));

    TtsUtils.ttsSetup();
    return FutureBuilder(
        future: init(context),
        builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return MaterialApp(
              navigatorKey: navigatorKey,
              localizationsDelegates: [
                LocStrDelegate(),
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: [
                const Locale('en', ''), // 英语
                const Locale('zh', 'CN'), // 简体中文
                // 其他支持的语言
              ],
              title: 'words',
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                // This is the theme of your application.
                //
                // Try running your application with "flutter run". You'll see the
                // application has a blue toolbar. Then, without quitting the app, try
                // changing the primarySwatch below to Colors.green and then invoke
                // "hot reload" (press "r" in the console where you ran "flutter run",
                // or simply save your changes to "hot reload" in a Flutter IDE).
                // Notice that the counter didn't reset back to zero; the application
                // is not restarted.
                  primarySwatch: Colors.blue,
                  scaffoldBackgroundColor: Color.fromRGBO(245, 245, 245, 1)
              ),
              home: RootPage(),
              onGenerateRoute: WordRouter.generateRouter,
            );
          } else {
            return Container();
          }
        }
    );
  }

  Future<bool> init(context) async {
    SystemConfig.user = User.namedConstructor(SystemConst.CURRENT_USER_ID, "silwoods", "Woods");
    SystemConfig.systemUser = User.namedConstructor(SystemConst.SYSTEM_USER_ID, "system", "System");

    if (SystemConfig.initiated == false)
      if(Platform.isIOS) {
        SystemConfig.DB_BASE_PATH = await iosCreatePath(SystemConfig.DB_RELATIVE_PATH);
        SystemConfig.SOUND_BASE_PATH = await iosCreatePath(SystemConfig.SOUND_RELATIVE_PATH);
        SystemConfig.LOG_PATH = await iosCreatePath(SystemConfig.LOG_RELATIVE_PATH);
        SystemConfig.initiated = true;
      } else if(Platform.isAndroid){
        SystemConfig.DB_BASE_PATH = SystemConfig.ANDROID_DB_BASE_PATH;
        SystemConfig.SOUND_BASE_PATH = SystemConfig.ANDROID_SOUND_BASE_PATH;
        SystemConfig.LOG_PATH = SystemConfig.ANDROID_LOG_PATH;
        SystemConfig.initiated = true;
      }
    SystemConfig.FULL_DB_PATH = SystemConfig.DB_BASE_PATH + Platform.pathSeparator + SystemConfig.DB_NAME;
    //请求权限
    PermissionUtil permissionUtil = new PermissionUtil(context);
    await permissionUtil.checkPermission();


    SystemConfig.studyEbbinghausPoint = await SystemBiz.shared.getEbbinghausPoint(SystemConfig.user!.id, EbbinghausPointType.Study);
    SystemConfig.repeatEbbinghausPoint = await  SystemBiz.shared.getEbbinghausPoint(SystemConfig.user!.id, EbbinghausPointType.Repeat);
    SystemConfig.sysProperties = await SystemBiz.shared.getSystemProperties(SystemConfig.systemUser!.id);
    SystemConfig.userProperties = await SystemBiz.shared.getSystemProperties(SystemConfig.user!.id);

    return true;

  }

  // Restart function
  void restartApp() {
    navigatorKey.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => MyApp()),
          (Route<dynamic> route) => false,
    );
  }

  Future<String> iosCreatePath(String last) async {
    final documentPath = await getDatabasesPath();
    final path = documentPath+ "/" + SystemConfig.IOS_PACKAGE_PATH + "/" +last;
    var file = Directory(path);
    try {
      bool exists = await file.exists();
      if (!exists) {
        await file.create(recursive: true);
      }
    } catch (e) {
      print(e);
    }
    return path;
  }

}
