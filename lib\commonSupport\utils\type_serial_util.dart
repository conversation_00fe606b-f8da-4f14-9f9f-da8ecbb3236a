class TypeSerialUtil{
  static dynamicToInt(dynamic value) {
    if (value == null) return null;
    if (value is int)	return value;
    if (value is String && value.length == 0) return null;
    return int.parse(value);
  }

  static dynamicToBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String && value.length > 0) {
      if (value.toLowerCase() == "true")
        return true;
      else
        return false;
    }
    if (value is int) {
      if (value == 0)
        return false;
      else
        return true;
    }
    return null;
  }

  static stringToBoolStrict(dynamic value) {
    if (value == null)
      return false;
    if (value.toLowerCase() == "true")
      return true;
    else
      return false;
  }

  static boolToInt(dynamic value){
    if (value == null) return null;
    if (value is bool) {
      if (value)
        return 1;
      else
        return 0;
    }
    return null;
  }
  static dynamicToDateTime(dynamic value){
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String && value.length == 0)	return null;

    if (value is String)
      return DateTime.fromMillisecondsSinceEpoch(int.parse(value));
    else if (value is int) {
      if  (value! >= 0 )
        return DateTime.fromMillisecondsSinceEpoch(value);
      else
        return DateTime.fromMillisecondsSinceEpoch(0);
    }
  }

  static dateTimeToInt(dynamic value){
    if (value == null) return null;
    if (value is DateTime)
      return value.millisecondsSinceEpoch;
    return null;
  }
}