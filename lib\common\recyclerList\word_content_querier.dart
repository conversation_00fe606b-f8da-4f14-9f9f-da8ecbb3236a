
import 'package:words/common/recyclerList/list_query_callable.dart';
import 'package:words/domain/word.dart';
import 'package:words/dto/word_dto.dart';

import '../../biz/word_biz.dart';

class WordContentQuerier extends ListQueryCallable {
  String? searchPattern;
  String? searchPattern2;
  Word? curWord;

  Future<List<WordDto>?> queryByPage(int? begin, int? pageSize) async {
    List<WordDto>? searchRes = await WordBiz.shared.searchWordContentByPatternByPage(searchPattern, searchPattern2, this.curWord, begin, pageSize);

    return searchRes;
  }

  Future<List<WordDto>?> filterContent(List? searchRes) async {
    return await searchRes as List<WordDto>;
  }

  Future<List<WordDto>?> inflateData(List? sourceList) async {
    return await sourceList == null ? null : sourceList!.cast<WordDto>().toList();
  }

  void setParam(Object param) {
    this.searchPattern = (param as Map)["searchString"];
    this.searchPattern2 = (param as Map)["searchString2"];
    this.curWord = (param as Map)["curWord"];
  }

}
