import 'package:words/common/system_config.dart';
import 'package:words/commonSupport/utils/collection_util.dart';
import 'package:words/commonSupport/utils/string_util.dart';
import 'package:words/commonSupport/utils/uuid_util.dart';
import 'package:words/dao/support/db_manager.dart';
import 'package:words/domain/book_content.dart';
import 'package:words/domain/book_mark.dart';

import 'package:words/domain/word.dart';

class BookMarkManager {
  //单例模式
  factory BookMarkManager() => _shared();
  static BookMarkManager get shared => _shared();
  static BookMarkManager? _instance;
  BookMarkManager._();
  static BookMarkManager _shared() {
    if (_instance == null) {
      _instance = BookMarkManager._();
    }
    return _instance!;
  }

  Future<void> moveBookMarkToPostion(String bookId, int bookMarkPostion, String userId) async {
    var queryRes = await DBManager.shared.tbBookMark.commQueryList(
        where: 'USER_ID = ? '
            'AND BOOK_ID = ? ',
        whereArgs: [SystemConfig.user!.id, bookId]
    );

    BookMark bookMark;
    if (CollectionUtil.nonEmpty(queryRes)) {

      bookMark = BookMark.fromJson(Map<String, dynamic>.of(queryRes![0]));
      bookMark.curSequence = bookMarkPostion;
      await DBManager.shared.tbBookMark.baseUpdateById(bookMark!.id!, bookMark.toJson());
    }
    else {
      bookMark = new BookMark();
      bookMark.bookId = bookId;
      bookMark.userId = userId;
      bookMark.id = UuidUtil.getUuid();
      bookMark.curSequence = bookMarkPostion;
      await DBManager.shared.tbBookMark.insert(bookMark);
    }

  }

  Future<BookMark?> getBookMarkPostion(String bookId, String userId) async {
    var queryRes = await DBManager.shared.tbBookMark.commQueryList(
        where: 'USER_ID = ? '
            'AND BOOK_ID = ? ',
        whereArgs: [SystemConfig.user!.id, bookId]
    );

    BookMark bookMark;
    if (CollectionUtil.nonEmpty(queryRes)) {
      bookMark = BookMark.fromJson(Map<String, dynamic>.of(queryRes![0]));
      return bookMark;
    } else
      return null;
  }

}