import 'dart:core';
import 'package:intl/intl.dart';

class DateUtil {
  static String date2String(DateTime? date, String pattern) {
    if (date != null) {
      return DateFormat(pattern).format(date);
    } else {
      return "";
    }
  }

  static DateTime getTodayBoundary() {
    var now = DateTime.now();
    var calendar = DateTime(now.year, now.month, now.day, 0, 0, 0, 0);

    if (now.hour >= 4) {
      calendar = calendar.add(Duration(days: 1));
    }
    calendar = calendar.add(Duration(hours: 4));

    return calendar;
  }

  static bool isNextToday(DateTime vDate) {
    var separatorTime = getTodayBoundary();
    return vDate.isAfter(separatorTime);
  }

  static int calMinPointOffset(int curStudyTime, int nextStudyTime) {
    int duration = nextStudyTime - curStudyTime;
    return (nextStudyTime - (duration * 0.03)).round() ;  //可提前3%的时候复习
  }
}
