// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cur_study_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CurStudyRecord _$CurStudyRecordFromJson(Map<String, dynamic> json) =>
    CurStudyRecord()
      ..id = json['ID'] as String?
      ..contentSearchId = json['CONTENT_SEARCH_ID'] as String?
      ..contentId = json['CONTENT_ID'] as String?
      ..userId = json['USER_ID'] as String?
      ..studyLevel = (json['STUDY_LEVEL'] as num?)?.toInt()
      ..curStudyLevel = (json['CUR_STUDY_LEVEL'] as num?)?.toInt()
      ..curAdjStudyLevel = (json['CUR_ADJ_STUDY_LEVEL'] as num?)?.toInt()
      ..firstJudge = TypeSerialUtil.dynamicToBool(json['FIRST_JUDGE'])
      ..firstStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['FIRST_STUDY_DATE'])
      ..addDate = TypeSerialUtil.dynamicToDateTime(json['ADD_DATE'])
      ..lastStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['LAST_STUDY_DATE'])
      ..minNextStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['MIN_NEXT_STUDY_DATE'])
      ..nextStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['NEXT_STUDY_DATE'])
      ..curEbbinghausStr = json['CUR_EBBINGHAUS_STR'] as String?
      ..backNextStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['BACK_NEXT_STUDY_DATE'])
      ..neverSeen = TypeSerialUtil.dynamicToBool(json['NEVER_SEEN'])
      ..oriStudyLevel = (json['ORI_STUDY_LEVEL'] as num?)?.toInt()
      ..oriNextStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['ORI_NEXT_STUDY_DATE'])
      ..oriMinNextStudyDate =
          TypeSerialUtil.dynamicToDateTime(json['ORI_MIN_NEXT_STUDY_DATE']);

Map<String, dynamic> _$CurStudyRecordToJson(CurStudyRecord instance) =>
    <String, dynamic>{
      'ID': instance.id,
      'CONTENT_SEARCH_ID': instance.contentSearchId,
      'CONTENT_ID': instance.contentId,
      'USER_ID': instance.userId,
      'STUDY_LEVEL': instance.studyLevel,
      'CUR_STUDY_LEVEL': instance.curStudyLevel,
      'CUR_ADJ_STUDY_LEVEL': instance.curAdjStudyLevel,
      'FIRST_JUDGE': TypeSerialUtil.boolToInt(instance.firstJudge),
      'FIRST_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.firstStudyDate),
      'ADD_DATE': TypeSerialUtil.dateTimeToInt(instance.addDate),
      'LAST_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.lastStudyDate),
      'MIN_NEXT_STUDY_DATE':
          TypeSerialUtil.dateTimeToInt(instance.minNextStudyDate),
      'NEXT_STUDY_DATE': TypeSerialUtil.dateTimeToInt(instance.nextStudyDate),
      'CUR_EBBINGHAUS_STR': instance.curEbbinghausStr,
      'BACK_NEXT_STUDY_DATE':
          TypeSerialUtil.dateTimeToInt(instance.backNextStudyDate),
      'NEVER_SEEN': TypeSerialUtil.boolToInt(instance.neverSeen),
      'ORI_STUDY_LEVEL': instance.oriStudyLevel,
      'ORI_NEXT_STUDY_DATE':
          TypeSerialUtil.dateTimeToInt(instance.oriNextStudyDate),
      'ORI_MIN_NEXT_STUDY_DATE':
          TypeSerialUtil.dateTimeToInt(instance.oriMinNextStudyDate),
    };
