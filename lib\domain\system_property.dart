import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'system_property.g.dart';

@JsonSerializable()
class SystemProperty {
	@Json<PERSON>ey(name: "ID")
	String? id;

	@<PERSON><PERSON><PERSON><PERSON>(name: "USER_ID")
	String? userId;

	@<PERSON><PERSON><PERSON><PERSON>(name: "PROPERTY_NAME")
	String? propertyName;

	@J<PERSON><PERSON><PERSON>(name: "PROPERTY_VALUE")
	String? propertyValue;

	SystemProperty();

	factory SystemProperty.fromJson(Map<String, dynamic> json) => _$SystemPropertyFromJson(json);

	Map<String, dynamic> toJson() => _$SystemPropertyToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}