import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import 'package:words/commonSupport/utils/string_util.dart';

part 'word_relation.g.dart';

@JsonSerializable()
class WordRelation {
	@Json<PERSON>ey(name: "ID")
	String? id;

	@<PERSON><PERSON><PERSON><PERSON>(name: "RELATION_ID")
	String? relationId;

	@J<PERSON><PERSON><PERSON>(name: "WORD_ID")
	String? wordId;

	@J<PERSON><PERSON><PERSON>(name: "OWNER_ID")
	String? ownerId;

	WordRelation();

	factory WordRelation.fromJson(Map<String, dynamic> json) => _$WordRelationFromJson(json);

	Map<String, dynamic> toJson() => _$WordRelationToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

	String? getEfficientWordId(String? oriWordId) {
		if (StringUtil.isEmpty(oriWordId)) return null;
		if (oriWordId == this.relationId) return this.wordId;
		if (oriWordId == this.wordId) return this.relationId;
		return null;
	}
}