import 'cur_study_record.dart';
import '../commonSupport/limited_list.dart';

class HistoryCurStudyRcdBuffer {
  LimitedList<CurStudyRecord>? historyCurRecords;
  int curIdx = -1;

  HistoryCurStudyRcdBuffer(int len) {
    this.historyCurRecords = LimitedList<CurStudyRecord>(len);
  }

  bool add(CurStudyRecord curStudyRecord) {
    if (curStudyRecord == null) {
      return false;
    }
    CurStudyRecord? lastRcd;
    try {
      lastRcd = this.historyCurRecords?.last;
    } catch (e) {
      lastRcd = null;
    }

    // 如果与最后一个相等，则不添加。这种情况出现在从NouseBuffer取词，又连续取得重复的词时
    if (lastRcd != null && lastRcd.primeDataEquals(curStudyRecord)) {
      return false;
    }

    bool result = this.historyCurRecords!.add(curStudyRecord);
    curIdx = historyCurRecords!.size - 1;
    return result;
  }

  CurStudyRecord? remove() {
    CurStudyRecord? result;
    try {
      result = this.historyCurRecords!.removeAt(0);
    } catch (e) {
      result = null;
    }
    curIdx = historyCurRecords!.size - 1;
    return result;
  }

  CurStudyRecord? last() {
    if (curIdx < 0) {
      return null;
    }
    if (curIdx >= historyCurRecords!.size) {
      curIdx = historyCurRecords!.size - 1;
      return null;
    }

    return historyCurRecords![curIdx--];
  }

  CurStudyRecord? next() {
    if (curIdx >= historyCurRecords!.size - 1) {
      return null;
    }
    if (curIdx < -1) {
      curIdx = -1;
      return null;
    }
    return historyCurRecords![++curIdx];
  }
}
