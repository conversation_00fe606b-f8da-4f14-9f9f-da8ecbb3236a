import 'package:words/dao/support/db_base_provider.dart';
import 'package:words/domain/statistics_level_sum.dart';

class TbStatisticsLevelSum extends DBBaseProvider {

  @override
  String get tableName => "STATISTICS_LEVEL_SUM";

  @override
  tableCreateSql() {
    return tableBaseString(tableName) +
        '''
        ID TEXT NOT NULL,
        USER_ID TEXT,
        STUDY_LEVEL INTEGER,
        DURATION_STR TEXT,
        REMBER_SUM INTEGER,
        FORGET_SUM INTEGER,
        BLURRY_SUM INTEGER,
        LAST_UPDATE_TIME INTEGER,
        POINT_CHG_TIME INTEGER,
        NEVER_SEEN INTEGER,
        START_STAT_TIME Integer
        )
        ''';
  }

}